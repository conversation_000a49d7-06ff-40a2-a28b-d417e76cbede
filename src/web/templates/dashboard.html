<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binance-Lighter 套利交易监控</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .price-display {
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            font-weight: bold;
        }
        .profit-positive {
            color: #28a745;
        }
        .profit-negative {
            color: #dc3545;
        }
        .status-running {
            color: #28a745;
        }
        .status-stopped {
            color: #dc3545;
        }
        .status-warning {
            color: #ffc107;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .alert-item {
            border-left: 4px solid #dc3545;
            margin-bottom: 10px;
        }
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .trade-item {
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff;
            transition: all 0.2s ease;
        }
        .trade-item:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .trade-profit-positive {
            border-left: 4px solid #28a745;
        }
        .trade-profit-negative {
            border-left: 4px solid #dc3545;
        }
        .trade-profit-neutral {
            border-left: 4px solid #6c757d;
        }
        .trade-time {
            font-size: 0.8em;
            color: #6c757d;
        }
        .trade-amount {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 连接状态指示器 -->
    <div class="connection-status">
        <span id="connectionStatus" class="badge bg-secondary">
            <i class="fas fa-circle"></i> 连接中...
        </span>
    </div>

    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary"></i>
                    Binance-Lighter 套利交易监控
                </h1>
                <p class="text-muted">实时监控套利交易系统状态和性能</p>
            </div>
        </div>

        <!-- 系统状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-power-off fa-2x mb-2" id="systemStatusIcon"></i>
                        <h5 class="card-title">系统状态</h5>
                        <p class="card-text" id="systemStatus">
                            <span class="loading-spinner"></span> 加载中...
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2 text-success"></i>
                        <h5 class="card-title">总盈亏</h5>
                        <p class="card-text price-display" id="totalProfit">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exchange-alt fa-2x mb-2 text-info"></i>
                        <h5 class="card-title">交易次数</h5>
                        <p class="card-text" id="totalTrades">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-percentage fa-2x mb-2 text-warning"></i>
                        <h5 class="card-title">成功率</h5>
                        <p class="card-text" id="successRate">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格和信号 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-coins"></i> 实时价格
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h6>Binance</h6>
                                <div class="price-display text-primary">
                                    买一: <span id="binanceBid">--</span><br>
                                    卖一: <span id="binanceAsk">--</span><br>
                                    最新: <span id="binanceLast">--</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6>Lighter</h6>
                                <div class="price-display text-success">
                                    买一: <span id="lighterBid">--</span><br>
                                    卖一: <span id="lighterAsk">--</span><br>
                                    最新: <span id="lighterLast">--</span>
                                    <small id="lighterLastSource" class="text-muted d-block" style="font-size: 0.7em;"></small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h6>价差MA: <span id="spreadMA" class="price-display">--</span></h6>
                            <div id="currentSignal" class="badge bg-secondary">无信号</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area"></i> 价差走势
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="spreadChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 风险管理和控制 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt"></i> 风险管理
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>风险指标</h6>
                                <ul class="list-unstyled">
                                    <li>日盈亏: <span id="dailyPnl" class="price-display">--</span></li>
                                    <li>风险敞口: <span id="totalExposure" class="price-display">--</span></li>
                                    <li>最大回撤: <span id="maxDrawdown" class="price-display">--</span></li>
                                    <li>日交易次数: <span id="dailyTrades">--</span></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>风险告警</h6>
                                <div id="riskAlerts" style="max-height: 150px; overflow-y: auto;">
                                    <div class="text-muted">暂无告警</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs"></i> 系统控制
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <button id="emergencyStopBtn" class="btn btn-danger btn-lg mb-3" style="width: 100%;">
                            <i class="fas fa-stop"></i> 紧急停止
                        </button>
                        <button id="resetEmergencyBtn" class="btn btn-warning btn-lg" style="width: 100%;" disabled>
                            <i class="fas fa-redo"></i> 重置停止
                        </button>
                        <hr>
                        <div class="text-muted">
                            <small>紧急停止将立即停止所有交易活动</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易记录和系统日志 -->
        <div class="row">
            <!-- 交易记录 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i> 交易记录
                        </h5>
                        <button id="refreshTradesBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="tradesContainer" style="height: 300px; overflow-y: auto;">
                            <div class="text-center text-muted">
                                <div class="loading-spinner"></div>
                                <div>加载交易记录...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统日志 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 系统日志
                        </h5>
                        <button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="systemLogs" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9em;">
                            <div class="text-muted">等待日志数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // WebSocket连接
        let ws = null;
        let reconnectInterval = null;
        let spreadChart = null;
        let spreadData = [];
        let maxDataPoints = 50;

        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('spreadChart').getContext('2d');
            spreadChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '价差比例',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }, {
                        label: '移动平均',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    },
                    plugins: {
                        legend: {
                            display: true
                        }
                    }
                }
            });
        }

        // 连接WebSocket
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                updateConnectionStatus('connected');

                // 订阅实时数据
                ws.send(JSON.stringify({type: 'subscribe'}));

                // 清除重连定时器
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }

                // 显示连接成功通知
                showNotification('实时数据连接已建立', 'success');
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                } catch (e) {
                    console.error('解析WebSocket消息失败:', e);
                }
            };

            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                updateConnectionStatus('disconnected');

                // 尝试重连
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(() => {
                        console.log('尝试重新连接WebSocket...');
                        connectWebSocket();
                    }, 3000); // 每3秒重连一次
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                updateConnectionStatus('error');
            };
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            switch (status) {
                case 'connected':
                    statusElement.className = 'badge bg-success';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 已连接';
                    break;
                case 'disconnected':
                    statusElement.className = 'badge bg-warning';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 重连中...';
                    break;
                case 'error':
                    statusElement.className = 'badge bg-danger';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接错误';
                    break;
                default:
                    statusElement.className = 'badge bg-secondary';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接中...';
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'price_update':
                    updatePricesOnly(data.data);
                    break;
                case 'status_update':
                    updateSystemStatus(data.data);
                    break;
                case 'connection_status':
                    updateConnectionInfo(data.data);
                    break;
                case 'log_update':
                    updateSystemLogs(data.data);
                    break;
                case 'trade_update':
                    handleTradeUpdate(data.data);
                    break;
                case 'emergency_stop':
                case 'emergency_reset':
                    showNotification(data.message, data.type === 'emergency_stop' ? 'danger' : 'success');
                    break;
                case 'subscription_confirmed':
                    console.log('订阅确认:', data.message);
                    break;
                case 'pong':
                    // 心跳响应
                    break;
                default:
                    console.log('未知消息类型:', data);
            }
        }

        // 高频更新价格数据（新增）
        function updatePricesOnly(priceData) {
            const binancePrices = priceData.binance_prices || {};
            const lighterPrices = priceData.lighter_prices || {};

            // 更新Binance价格
            if (binancePrices.bid) document.getElementById('binanceBid').textContent = binancePrices.bid;
            if (binancePrices.ask) document.getElementById('binanceAsk').textContent = binancePrices.ask;
            if (binancePrices.last) document.getElementById('binanceLast').textContent = binancePrices.last;

            // 更新Lighter价格
            if (lighterPrices.bid) document.getElementById('lighterBid').textContent = lighterPrices.bid;
            if (lighterPrices.ask) document.getElementById('lighterAsk').textContent = lighterPrices.ask;
            if (lighterPrices.last) document.getElementById('lighterLast').textContent = lighterPrices.last;

            // 更新Lighter价格来源信息
            const lighterLastElement = document.getElementById('lighterLast');
            const lighterSourceElement = document.getElementById('lighterLastSource');

            if (lighterPrices.last && lighterPrices.last !== '--') {
                // 判断是否为买卖价中间价
                const bid = parseFloat(lighterPrices.bid);
                const ask = parseFloat(lighterPrices.ask);
                const last = parseFloat(lighterPrices.last);

                if (bid && ask && Math.abs(last - (bid + ask) / 2) < 0.01) {
                    lighterSourceElement.textContent = '(买卖价中间价)';
                    lighterSourceElement.className = 'text-warning d-block';
                } else {
                    lighterSourceElement.textContent = '(真实成交价)';
                    lighterSourceElement.className = 'text-success d-block';
                }
            } else {
                lighterSourceElement.textContent = '';
            }

            // 更新价差MA
            const spreadMA = priceData.spread_ma_value;
            if (spreadMA !== undefined) {
                document.getElementById('spreadMA').textContent = spreadMA ? spreadMA.toFixed(6) : '--';
            }

            // 更新当前信号
            const currentSignal = priceData.current_signal;
            const signalElement = document.getElementById('currentSignal');
            if (currentSignal) {
                const signalType = currentSignal.signal_type;
                let badgeClass = 'bg-secondary';
                let signalText = '无信号';

                if (signalType === 'buy') {
                    badgeClass = 'bg-success';
                    signalText = '买入信号';
                } else if (signalType === 'sell') {
                    badgeClass = 'bg-danger';
                    signalText = '卖出信号';
                }

                signalElement.className = `badge ${badgeClass}`;
                signalElement.textContent = signalText;
            }

            // 更新价差图表
            updateSpreadChart(binancePrices.last, lighterPrices.last, spreadMA);
        }

        // 更新连接状态信息（新增）
        function updateConnectionInfo(connectionData) {
            // 更新连接状态指示器
            const statusElement = document.getElementById('connectionStatus');
            const allConnected = connectionData.binance_connected &&
                                connectionData.lighter_connected &&
                                connectionData.websocket_connected;

            if (allConnected && connectionData.is_running) {
                statusElement.className = 'badge bg-success';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> 全部连接';
            } else if (connectionData.is_running) {
                statusElement.className = 'badge bg-warning';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> 部分连接';
            } else {
                statusElement.className = 'badge bg-danger';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> 已停止';
            }
        }

        // 更新系统状态
        function updateSystemStatus(status) {
            // 更新系统运行状态
            const systemStatusElement = document.getElementById('systemStatus');
            const systemStatusIcon = document.getElementById('systemStatusIcon');

            if (status.is_running) {
                systemStatusElement.innerHTML = '<span class="status-running">运行中</span>';
                systemStatusIcon.className = 'fas fa-power-off fa-2x mb-2 text-success';
            } else {
                systemStatusElement.innerHTML = '<span class="status-stopped">已停止</span>';
                systemStatusIcon.className = 'fas fa-power-off fa-2x mb-2 text-danger';
            }

            // 更新统计信息
            const stats = status.stats || {};
            document.getElementById('totalProfit').innerHTML =
                `<span class="${stats.total_profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                    ${(stats.total_profit || 0).toFixed(4)} USDT
                </span>`;

            document.getElementById('totalTrades').textContent = stats.total_trades || 0;

            const successRate = stats.total_trades > 0 ?
                ((stats.successful_trades || 0) / stats.total_trades * 100).toFixed(1) : 0;
            document.getElementById('successRate').textContent = `${successRate}%`;

            // 更新风险信息
            const riskStatus = status.risk_status || {};
            document.getElementById('dailyPnl').innerHTML =
                `<span class="${riskStatus.daily_pnl >= 0 ? 'profit-positive' : 'profit-negative'}">
                    ${(riskStatus.daily_pnl || 0).toFixed(4)} USDT
                </span>`;

            document.getElementById('totalExposure').textContent =
                `${(riskStatus.total_exposure || 0).toFixed(2)} USDT`;

            document.getElementById('maxDrawdown').textContent =
                `${((riskStatus.max_drawdown || 0) * 100).toFixed(2)}%`;

            document.getElementById('dailyTrades').textContent = riskStatus.daily_trades || 0;

            // 更新风险告警
            updateRiskAlerts(riskStatus.recent_alerts || []);

            // 更新紧急停止按钮状态
            updateEmergencyButtons(riskStatus.is_emergency_stop);
        }

        // 更新风险告警
        function updateRiskAlerts(alerts) {
            const alertsContainer = document.getElementById('riskAlerts');

            if (alerts.length === 0) {
                alertsContainer.innerHTML = '<div class="text-muted">暂无告警</div>';
                return;
            }

            const alertsHtml = alerts.map(alert => `
                <div class="alert alert-${getLevelClass(alert.level)} alert-item py-2 px-3">
                    <small>
                        <strong>${alert.risk_type}:</strong> ${alert.message}
                        <br>
                        <span class="text-muted">${new Date(alert.timestamp * 1000).toLocaleTimeString()}</span>
                    </small>
                </div>
            `).join('');

            alertsContainer.innerHTML = alertsHtml;
        }

        // 获取告警级别对应的Bootstrap类
        function getLevelClass(level) {
            const levelMap = {
                'low': 'info',
                'medium': 'warning',
                'high': 'warning',
                'critical': 'danger'
            };
            return levelMap[level] || 'secondary';
        }

        // 更新紧急停止按钮
        function updateEmergencyButtons(isEmergencyStop) {
            const emergencyBtn = document.getElementById('emergencyStopBtn');
            const resetBtn = document.getElementById('resetEmergencyBtn');

            if (isEmergencyStop) {
                emergencyBtn.disabled = true;
                emergencyBtn.innerHTML = '<i class="fas fa-stop"></i> 已停止';
                resetBtn.disabled = false;
            } else {
                emergencyBtn.disabled = false;
                emergencyBtn.innerHTML = '<i class="fas fa-stop"></i> 紧急停止';
                resetBtn.disabled = true;
            }
        }

        // 更新价差图表
        function updateSpreadChart(binancePrice, lighterPrice, spreadMA) {
            if (!binancePrice || !lighterPrice || !spreadChart) return;

            const diffRate = (binancePrice / lighterPrice) - 1;
            const now = new Date().toLocaleTimeString();

            // 添加新数据点
            spreadChart.data.labels.push(now);
            spreadChart.data.datasets[0].data.push(diffRate);
            spreadChart.data.datasets[1].data.push(spreadMA);

            // 限制数据点数量
            if (spreadChart.data.labels.length > maxDataPoints) {
                spreadChart.data.labels.shift();
                spreadChart.data.datasets[0].data.shift();
                spreadChart.data.datasets[1].data.shift();
            }

            spreadChart.update('none');
        }

        // 处理交易更新（新增）
        function handleTradeUpdate(tradeData) {
            console.log('收到交易更新:', tradeData);

            // 显示交易通知
            const profit = parseFloat(tradeData.actual_profit || 0);
            const profitText = profit >= 0 ? `+${profit.toFixed(4)}` : profit.toFixed(4);
            const notificationType = profit >= 0 ? 'success' : 'warning';

            showNotification(
                `新交易完成: ${profitText} USDT`,
                notificationType
            );

            // 立即刷新交易记录
            loadTradeRecords();
        }

        // 格式化时间为本地时间，精确到毫秒
        function formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
        }

        // 更新交易记录
        function updateTradeRecords(trades) {
            const tradesContainer = document.getElementById('tradesContainer');

            if (!trades || trades.length === 0) {
                tradesContainer.innerHTML = '<div class="text-center text-muted">暂无交易记录</div>';
                return;
            }

            const tradesHtml = trades.map(trade => {
                const timestamp = formatTimestamp(trade.timestamp);
                const profit = parseFloat(trade.actual_profit || 0);
                const profitClass = profit > 0 ? 'trade-profit-positive' :
                                  profit < 0 ? 'trade-profit-negative' : 'trade-profit-neutral';
                const profitColor = profit > 0 ? 'text-success' :
                                   profit < 0 ? 'text-danger' : 'text-muted';

                // 格式化交易类型显示
                const tradeTypeDisplay = trade.trade_type === 'buy_arbitrage' ? '买入套利' :
                                       trade.trade_type === 'sell_arbitrage' ? '卖出套利' :
                                       trade.trade_type || 'Unknown';

                // 计算执行时长
                const executionTime = trade.execution_time_ms ? `${trade.execution_time_ms}ms` : '--';

                // 格式化买入和卖出时间
                const binanceTime = trade.binance_timestamp ? formatTimestamp(trade.binance_timestamp) : '--';
                const lighterTime = trade.lighter_timestamp ? formatTimestamp(trade.lighter_timestamp) : '--';

                // 构建详细的价格和时间信息
                let detailsHtml = '';
                if (trade.binance_price && trade.lighter_price) {
                    detailsHtml = `
                        <div class="mt-2 small">
                            <div class="row mb-2">
                                <div class="col-6">
                                    <span class="text-muted">Binance ${trade.binance_side || ''}:</span>
                                    <div class="fw-bold">${parseFloat(trade.binance_price).toFixed(2)} USDT</div>
                                    <div class="trade-time text-info" style="font-size: 0.8em;">${binanceTime}</div>
                                </div>
                                <div class="col-6">
                                    <span class="text-muted">Lighter ${trade.lighter_side || ''}:</span>
                                    <div class="fw-bold">${parseFloat(trade.lighter_price).toFixed(2)} USDT</div>
                                    <div class="trade-time text-info" style="font-size: 0.8em;">${lighterTime}</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <span class="text-muted">数量:</span> ${parseFloat(trade.binance_quantity || trade.lighter_quantity || 0).toFixed(4)}
                                </div>
                                <div class="col-6">
                                    <span class="text-muted">执行时长:</span> <span class="text-info">${executionTime}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }

                return `
                    <div class="trade-item ${profitClass} mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold">${tradeTypeDisplay}</div>
                                <div class="trade-time small text-primary">${timestamp}</div>
                                <div class="small text-muted">${trade.symbol || 'BTC/USDT'}</div>
                            </div>
                            <div class="text-end">
                                <div class="trade-amount ${profitColor} fw-bold">
                                    ${profit >= 0 ? '+' : ''}${profit.toFixed(4)} USDT
                                </div>
                                <div class="small text-muted">
                                    ${trade.status === 'executed' ? '已完成' :
                                      trade.status === 'pending' ? '待执行' :
                                      trade.status === 'failed' ? '失败' :
                                      trade.status || 'Unknown'}
                                </div>
                            </div>
                        </div>
                        ${detailsHtml}
                    </div>
                `;
            }).join('');

            tradesContainer.innerHTML = tradesHtml;
        }

        // 加载交易记录
        async function loadTradeRecords() {
            try {
                const response = await fetch('/api/trades?limit=20');
                if (response.ok) {
                    const data = await response.json();
                    updateTradeRecords(data.trades || []);
                } else {
                    console.error('获取交易记录失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载交易记录失败:', error);
                document.getElementById('tradesContainer').innerHTML =
                    '<div class="text-center text-danger">加载失败</div>';
            }
        }

        // 更新系统日志
        function updateSystemLogs(logEntry) {
            const logsContainer = document.getElementById('systemLogs');

            // 如果是第一条日志，清除"等待日志数据"提示
            const waitingMessage = logsContainer.querySelector('.text-muted');
            if (waitingMessage && waitingMessage.textContent === '等待日志数据...') {
                waitingMessage.remove();
            }

            // 创建日志条目
            const logElement = document.createElement('div');
            // 使用精确到毫秒的时间格式
            const timestamp = formatTimestamp(new Date(logEntry.timestamp * 1000));
            const levelClass = getLevelClass(logEntry.level.toLowerCase());

            logElement.innerHTML = `
                <div class="log-entry mb-1">
                    <span class="text-muted small">[${timestamp}]</span>
                    <span class="badge bg-${levelClass} me-1">${logEntry.level}</span>
                    <span>${logEntry.message}</span>
                    ${logEntry.data && Object.keys(logEntry.data).length > 0 ?
                        `<small class="text-muted d-block ms-3">${JSON.stringify(logEntry.data)}</small>` : ''}
                </div>
            `;

            // 添加到日志容器
            logsContainer.appendChild(logElement);

            // 限制日志条目数量
            const logEntries = logsContainer.querySelectorAll('.log-entry');
            if (logEntries.length > 100) {
                logEntries[0].remove();
            }

            // 自动滚动到底部
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            const logsContainer = document.getElementById('systemLogs');
            logsContainer.innerHTML = '<div class="text-muted">日志已清空</div>';
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 60px; right: 20px; z-index: 1050; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // 紧急停止
        async function emergencyStop() {
            if (!confirm('确定要触发紧急停止吗？这将立即停止所有交易活动。')) {
                return;
            }

            try {
                const response = await fetch('/api/emergency_stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('紧急停止已触发', 'warning');
                } else {
                    showNotification(`紧急停止失败: ${result.error}`, 'danger');
                }
            } catch (error) {
                showNotification(`紧急停止失败: ${error.message}`, 'danger');
            }
        }

        // 重置紧急停止
        async function resetEmergency() {
            if (!confirm('确定要重置紧急停止状态吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/reset_emergency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('紧急停止已重置', 'success');
                } else {
                    showNotification(`重置失败: ${result.error}`, 'danger');
                }
            } catch (error) {
                showNotification(`重置失败: ${error.message}`, 'danger');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initChart();

            // 连接WebSocket
            connectWebSocket();

            // 主动获取初始状态数据
            loadInitialData();

            // 加载交易记录
            loadTradeRecords();

            // 绑定按钮事件
            document.getElementById('emergencyStopBtn').addEventListener('click', emergencyStop);
            document.getElementById('resetEmergencyBtn').addEventListener('click', resetEmergency);
            document.getElementById('refreshTradesBtn').addEventListener('click', loadTradeRecords);
            document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);

            // 定期刷新交易记录（降低频率，因为现在有实时推送）
            setInterval(() => {
                loadTradeRecords();
            }, 60000); // 每60秒刷新一次（作为备份机制）

            // 定期发送心跳
            setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({type: 'ping'}));
                }
            }, 30000);
        });

        // 加载初始数据
        async function loadInitialData() {
            try {
                // 获取系统状态
                const statusResponse = await fetch('/api/status');
                if (statusResponse.ok) {
                    const statusData = await statusResponse.json();
                    console.log('获取到初始状态数据:', statusData);
                    updateSystemStatus(statusData);
                }

                // 获取价格数据
                const pricesResponse = await fetch('/api/prices');
                if (pricesResponse.ok) {
                    const pricesData = await pricesResponse.json();
                    console.log('获取到初始价格数据:', pricesData);
                    updatePricesOnly(pricesData);
                }

                // 获取初始日志
                const logsResponse = await fetch('/api/logs?limit=50');
                if (logsResponse.ok) {
                    const logsData = await logsResponse.json();
                    if (logsData.logs && logsData.logs.length > 0) {
                        const logsContainer = document.getElementById('systemLogs');
                        logsContainer.innerHTML = ''; // 清空等待消息

                        logsData.logs.forEach(logEntry => {
                            updateSystemLogs(logEntry);
                        });
                    }
                }

            } catch (error) {
                console.error('加载初始数据失败:', error);
                showNotification('加载初始数据失败: ' + error.message, 'warning');
            }
        }

        // 页面卸载时关闭WebSocket
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>