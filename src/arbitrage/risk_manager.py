"""
风险管理模块

提供套利交易的风险控制功能
"""

import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import structlog

logger = structlog.get_logger(__name__)


@dataclass
class RiskAlert:
    """风险告警"""
    risk_type: str
    level: str  # 'low', 'medium', 'high', 'critical'
    message: str
    current_value: float
    threshold: float
    timestamp: float


class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化风险管理器
        
        Args:
            config: 风险管理配置
        """
        self.config = config
        
        # 风险限制
        self.max_exposure = config.get('max_exposure', 1000)  # 最大风险敞口
        self.position_limit_pct = config.get('position_limit_pct', 0.8)  # 仓位限制百分比
        self.stop_loss_pct = config.get('stop_loss_pct', 0.05)  # 止损百分比
        self.max_loss_per_day = config.get('max_loss_per_day', 100)  # 单日最大亏损
        self.price_deviation_threshold = config.get('price_deviation_threshold', 0.1)  # 价格偏差阈值
        self.latency_threshold = config.get('latency_threshold', 1000)  # 延迟阈值(毫秒)
        
        # 风险状态
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.last_reset_date = time.strftime('%Y-%m-%d')
        self.risk_alerts: List[RiskAlert] = []
        self.is_emergency_stop = False
        
        # 统计数据
        self.total_exposure = 0.0
        self.max_drawdown = 0.0
        self.peak_value = 0.0
        
        logger.info("风险管理器初始化完成", config=config)
    
    def check_risk(self, binance_balance: Dict[str, Any], 
                   lighter_balance: Dict[str, Any],
                   current_positions: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合风险检查
        
        Args:
            binance_balance: Binance余额
            lighter_balance: Lighter余额
            current_positions: 当前持仓
            
        Returns:
            风险检查结果
        """
        try:
            # 重置日统计（如果需要）
            self._reset_daily_stats_if_needed()
            
            # 检查紧急停止状态
            if self.is_emergency_stop:
                return {
                    'allowed': False,
                    'reason': '紧急停止状态',
                    'risk_level': 'critical'
                }
            
            # 执行各项风险检查
            risk_checks = [
                self._check_daily_loss_limit(),
                self._check_position_limits(binance_balance, lighter_balance),
                self._check_exposure_limits(current_positions),
                self._check_balance_sufficiency(binance_balance, lighter_balance),
                self._check_drawdown_limits()
            ]
            
            # 汇总风险检查结果
            failed_checks = [check for check in risk_checks if not check['passed']]
            
            if failed_checks:
                # 记录风险告警
                for check in failed_checks:
                    self._add_risk_alert(
                        risk_type=check['type'],
                        level=check['level'],
                        message=check['reason'],
                        current_value=check.get('current_value', 0),
                        threshold=check.get('threshold', 0)
                    )
                
                # 返回最高风险级别的检查结果
                highest_risk = max(failed_checks, key=lambda x: self._risk_level_priority(x['level']))
                
                return {
                    'allowed': False,
                    'reason': highest_risk['reason'],
                    'risk_level': highest_risk['level'],
                    'failed_checks': failed_checks
                }
            
            return {
                'allowed': True,
                'reason': '风险检查通过',
                'risk_level': 'low'
            }
            
        except Exception as e:
            logger.error("风险检查失败", error=str(e))
            return {
                'allowed': False,
                'reason': f'风险检查异常: {str(e)}',
                'risk_level': 'critical'
            }
    
    def _reset_daily_stats_if_needed(self) -> None:
        """如果需要，重置日统计"""
        current_date = time.strftime('%Y-%m-%d')
        if current_date != self.last_reset_date:
            logger.info("重置日统计", date=current_date)
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.last_reset_date = current_date
            # 清理过期告警
            self._cleanup_old_alerts()
    
    def _check_daily_loss_limit(self) -> Dict[str, Any]:
        """检查日亏损限制"""
        if self.daily_pnl < -self.max_loss_per_day:
            return {
                'passed': False,
                'type': 'daily_loss',
                'level': 'critical',
                'reason': f'日亏损超限: {self.daily_pnl:.2f} < -{self.max_loss_per_day}',
                'current_value': abs(self.daily_pnl),
                'threshold': self.max_loss_per_day
            }
        
        # 警告级别检查（80%阈值）
        warning_threshold = self.max_loss_per_day * 0.8
        if self.daily_pnl < -warning_threshold:
            return {
                'passed': False,
                'type': 'daily_loss',
                'level': 'high',
                'reason': f'日亏损接近限制: {self.daily_pnl:.2f}',
                'current_value': abs(self.daily_pnl),
                'threshold': warning_threshold
            }
        
        return {'passed': True}
    
    def _check_position_limits(self, binance_balance: Dict[str, Any], 
                              lighter_balance: Dict[str, Any]) -> Dict[str, Any]:
        """检查仓位限制"""
        try:
            # 获取总资产
            total_assets = self._calculate_total_assets(binance_balance, lighter_balance)
            
            if total_assets <= 0:
                return {
                    'passed': False,
                    'type': 'position',
                    'level': 'critical',
                    'reason': '无法计算总资产或资产为零',
                    'current_value': total_assets,
                    'threshold': 0
                }
            
            # 计算当前仓位比例
            current_position_value = self._calculate_position_value(binance_balance, lighter_balance)
            position_ratio = current_position_value / total_assets
            
            if position_ratio > self.position_limit_pct:
                return {
                    'passed': False,
                    'type': 'position',
                    'level': 'high',
                    'reason': f'仓位比例超限: {position_ratio:.2%} > {self.position_limit_pct:.2%}',
                    'current_value': position_ratio,
                    'threshold': self.position_limit_pct
                }
            
            return {'passed': True}
            
        except Exception as e:
            logger.error("仓位检查失败", error=str(e))
            return {
                'passed': False,
                'type': 'position',
                'level': 'medium',
                'reason': f'仓位检查异常: {str(e)}',
                'current_value': 0,
                'threshold': 0
            }
    
    def _check_exposure_limits(self, current_positions: Dict[str, Any]) -> Dict[str, Any]:
        """检查风险敞口限制"""
        try:
            # 计算当前风险敞口
            current_exposure = self._calculate_exposure(current_positions)
            self.total_exposure = current_exposure
            
            if current_exposure > self.max_exposure:
                return {
                    'passed': False,
                    'type': 'exposure',
                    'level': 'high',
                    'reason': f'风险敞口超限: {current_exposure:.2f} > {self.max_exposure}',
                    'current_value': current_exposure,
                    'threshold': self.max_exposure
                }
            
            # 警告级别检查（80%阈值）
            warning_threshold = self.max_exposure * 0.8
            if current_exposure > warning_threshold:
                return {
                    'passed': False,
                    'type': 'exposure',
                    'level': 'medium',
                    'reason': f'风险敞口接近限制: {current_exposure:.2f}',
                    'current_value': current_exposure,
                    'threshold': warning_threshold
                }
            
            return {'passed': True}
            
        except Exception as e:
            logger.error("敞口检查失败", error=str(e))
            return {
                'passed': False,
                'type': 'exposure',
                'level': 'medium',
                'reason': f'敞口检查异常: {str(e)}',
                'current_value': 0,
                'threshold': 0
            }
    
    def _check_balance_sufficiency(self, binance_balance: Dict[str, Any], 
                                  lighter_balance: Dict[str, Any]) -> Dict[str, Any]:
        """检查余额充足性"""
        try:
            # 检查主要交易币种余额
            base_currency = 'BTC'  # 可以从配置获取
            quote_currency = 'USDT'
            
            # 检查Binance余额
            binance_base = binance_balance.get(base_currency, {}).get('free', 0)
            binance_quote = binance_balance.get(quote_currency, {}).get('free', 0)
            
            # 检查Lighter余额
            lighter_base = lighter_balance.get(base_currency, {}).get('free', 0)
            lighter_quote = lighter_balance.get(quote_currency, {}).get('free', 0)
            
            # 设置最小余额要求
            min_base_balance = 0.001  # 最小BTC余额
            min_quote_balance = 100   # 最小USDT余额
            
            if binance_base < min_base_balance or binance_quote < min_quote_balance:
                return {
                    'passed': False,
                    'type': 'balance',
                    'level': 'high',
                    'reason': f'Binance余额不足: {base_currency}={binance_base}, {quote_currency}={binance_quote}',
                    'current_value': min(binance_base, binance_quote),
                    'threshold': min(min_base_balance, min_quote_balance)
                }
            
            if lighter_base < min_base_balance or lighter_quote < min_quote_balance:
                return {
                    'passed': False,
                    'type': 'balance',
                    'level': 'high',
                    'reason': f'Lighter余额不足: {base_currency}={lighter_base}, {quote_currency}={lighter_quote}',
                    'current_value': min(lighter_base, lighter_quote),
                    'threshold': min(min_base_balance, min_quote_balance)
                }
            
            return {'passed': True}
            
        except Exception as e:
            logger.error("余额检查失败", error=str(e))
            return {
                'passed': False,
                'type': 'balance',
                'level': 'medium',
                'reason': f'余额检查异常: {str(e)}',
                'current_value': 0,
                'threshold': 0
            }
    
    def _check_drawdown_limits(self) -> Dict[str, Any]:
        """检查回撤限制"""
        try:
            # 更新峰值
            current_value = self.daily_pnl  # 简化处理，实际应该是累计净值
            if current_value > self.peak_value:
                self.peak_value = current_value
            
            # 计算回撤
            if self.peak_value > 0:
                drawdown = (self.peak_value - current_value) / self.peak_value
                self.max_drawdown = max(self.max_drawdown, drawdown)
                
                # 检查回撤限制
                max_allowed_drawdown = self.stop_loss_pct
                if drawdown > max_allowed_drawdown:
                    return {
                        'passed': False,
                        'type': 'drawdown',
                        'level': 'critical',
                        'reason': f'回撤超限: {drawdown:.2%} > {max_allowed_drawdown:.2%}',
                        'current_value': drawdown,
                        'threshold': max_allowed_drawdown
                    }
            
            return {'passed': True}
            
        except Exception as e:
            logger.error("回撤检查失败", error=str(e))
            return {
                'passed': False,
                'type': 'drawdown',
                'level': 'medium',
                'reason': f'回撤检查异常: {str(e)}',
                'current_value': 0,
                'threshold': 0
            }
    
    def _calculate_total_assets(self, binance_balance: Dict[str, Any], 
                               lighter_balance: Dict[str, Any]) -> float:
        """计算总资产"""
        try:
            total = 0.0
            
            # 计算Binance资产
            for currency, balance_info in binance_balance.items():
                if currency == 'USDT':
                    total += balance_info.get('total', 0)
                elif currency == 'BTC':
                    # 简化处理，假设BTC价格为50000 USDT
                    btc_price = 50000  # 实际应该从市场数据获取
                    total += balance_info.get('total', 0) * btc_price
            
            # 计算Lighter资产（类似处理）
            for currency, balance_info in lighter_balance.items():
                if currency == 'USDT':
                    total += balance_info.get('total', 0)
                elif currency == 'BTC':
                    btc_price = 50000
                    total += balance_info.get('total', 0) * btc_price
            
            return total
            
        except Exception as e:
            logger.error("计算总资产失败", error=str(e))
            return 0.0
    
    def _calculate_position_value(self, binance_balance: Dict[str, Any], 
                                 lighter_balance: Dict[str, Any]) -> float:
        """计算当前仓位价值"""
        try:
            # 简化处理，实际应该计算所有非现金资产的价值
            position_value = 0.0
            
            # 计算BTC仓位价值
            btc_price = 50000  # 实际应该从市场数据获取
            
            binance_btc = binance_balance.get('BTC', {}).get('total', 0)
            lighter_btc = lighter_balance.get('BTC', {}).get('total', 0)
            
            position_value = (binance_btc + lighter_btc) * btc_price
            
            return position_value
            
        except Exception as e:
            logger.error("计算仓位价值失败", error=str(e))
            return 0.0
    
    def _calculate_exposure(self, current_positions: Dict[str, Any]) -> float:
        """计算风险敞口"""
        try:
            exposure = 0.0
            
            # 计算所有活跃订单的风险敞口
            for exchange, order_info in current_positions.items():
                order_value = order_info.amount * order_info.price
                exposure += order_value
            
            return exposure
            
        except Exception as e:
            logger.error("计算风险敞口失败", error=str(e))
            return 0.0
    
    def _risk_level_priority(self, level: str) -> int:
        """风险级别优先级"""
        priority_map = {
            'low': 1,
            'medium': 2,
            'high': 3,
            'critical': 4
        }
        return priority_map.get(level, 0)
    
    def _add_risk_alert(self, risk_type: str, level: str, message: str,
                       current_value: float, threshold: float) -> None:
        """添加风险告警"""
        alert = RiskAlert(
            risk_type=risk_type,
            level=level,
            message=message,
            current_value=current_value,
            threshold=threshold,
            timestamp=time.time()
        )
        
        self.risk_alerts.append(alert)
        
        # 限制告警数量
        if len(self.risk_alerts) > 100:
            self.risk_alerts = self.risk_alerts[-50:]
        
        logger.warning("风险告警", alert=alert)
    
    def _cleanup_old_alerts(self) -> None:
        """清理过期告警"""
        current_time = time.time()
        # 保留24小时内的告警
        self.risk_alerts = [
            alert for alert in self.risk_alerts
            if current_time - alert.timestamp < 86400
        ]
    
    def update_daily_pnl(self, pnl: float) -> None:
        """更新日盈亏"""
        self.daily_pnl += pnl
        self.daily_trades += 1
        
        logger.info("更新日盈亏", daily_pnl=self.daily_pnl, daily_trades=self.daily_trades)
    
    def trigger_emergency_stop(self, reason: str) -> None:
        """触发紧急停止"""
        self.is_emergency_stop = True
        
        self._add_risk_alert(
            risk_type='emergency',
            level='critical',
            message=f'紧急停止: {reason}',
            current_value=0,
            threshold=0
        )
        
        logger.critical("触发紧急停止", reason=reason)
    
    def reset_emergency_stop(self) -> None:
        """重置紧急停止状态"""
        self.is_emergency_stop = False
        logger.info("重置紧急停止状态")
    
    def get_risk_status(self) -> Dict[str, Any]:
        """获取风险状态"""
        return {
            'is_emergency_stop': self.is_emergency_stop,
            'daily_pnl': self.daily_pnl,
            'daily_trades': self.daily_trades,
            'total_exposure': self.total_exposure,
            'max_drawdown': self.max_drawdown,
            'recent_alerts': self.risk_alerts[-10:],  # 最近10个告警
            'risk_limits': {
                'max_exposure': self.max_exposure,
                'position_limit_pct': self.position_limit_pct,
                'stop_loss_pct': self.stop_loss_pct,
                'max_loss_per_day': self.max_loss_per_day
            }
        }
    
    def get_risk_metrics(self) -> Dict[str, float]:
        """获取风险指标"""
        return {
            'daily_pnl': self.daily_pnl,
            'daily_trades': self.daily_trades,
            'total_exposure': self.total_exposure,
            'max_drawdown': self.max_drawdown,
            'exposure_utilization': self.total_exposure / self.max_exposure if self.max_exposure > 0 else 0,
            'daily_loss_utilization': abs(self.daily_pnl) / self.max_loss_per_day if self.daily_pnl < 0 else 0
        } 