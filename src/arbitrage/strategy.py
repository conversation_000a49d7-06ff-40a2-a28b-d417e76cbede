"""
套利策略模块

实现Binance-Lighter套利交易策略
"""

import asyncio
import time
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import structlog

from ..utils.indicators import SpreadAnalyzer, MovingAverage
from ..utils.logger import trading_logger

logger = structlog.get_logger(__name__)


class SignalType(Enum):
    """交易信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


@dataclass
class ArbitrageSignal:
    """套利信号"""
    signal_type: SignalType
    binance_price: float
    lighter_price: float
    diff_rate: float
    ma_value: float
    confidence: float
    timestamp: float
    reason: str
    expected_profit: Optional[float] = None  # 预期利润
    spread_pct: Optional[float] = None  # 价差百分比


@dataclass
class OrderInfo:
    """订单信息"""
    exchange: str
    order_id: str
    symbol: str
    side: str
    amount: float
    price: float
    status: str
    timestamp: float
    trade_id: Optional[str] = None  # 添加交易ID字段


class ArbitrageStrategy:
    """套利交易策略"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化套利策略

        Args:
            config: 配置字典
        """
        self.config = config

        # 交易配置
        self.symbol = config.get('symbol', 'BTC/USDT')
        self.ma_period = config.get('ma_period', 20)
        self.min_profit_threshold = config.get('min_profit_threshold', 0.001)
        self.max_spread_threshold = config.get('max_spread_threshold', 0.02)
        self.max_trade_amount = config.get('max_trade_amount', 0.01)
        self.min_trade_amount = config.get('min_trade_amount', 0.001)

        # 初始化指标计算器
        self.spread_analyzer = SpreadAnalyzer(self.ma_period)

        # 当前状态
        self.current_signal: Optional[ArbitrageSignal] = None
        self.active_orders: Dict[str, OrderInfo] = {}  # exchange -> OrderInfo
        self.last_trade_time = 0
        self.is_hedging = False

        # 价格数据
        self.binance_prices = {'bid': 0, 'ask': 0, 'last': 0}
        self.lighter_prices = {'bid': 0, 'ask': 0, 'last': 0}

        logger.info("套利策略初始化完成", symbol=self.symbol, ma_period=self.ma_period)

        # 清理可能残留的活跃订单（特别是在纸上交易模式下）
        self.clear_active_orders()

    def update_binance_prices(self, bid: float, ask: float, last: float = None) -> None:
        """
        更新Binance价格

        Args:
            bid: 买一价
            ask: 卖一价
            last: 最新成交价
        """
        self.binance_prices['bid'] = bid
        self.binance_prices['ask'] = ask
        if last:
            self.binance_prices['last'] = last

        logger.debug("Binance价格更新", **self.binance_prices)

    def update_lighter_prices(self, bid: float, ask: float, last: float = None) -> None:
        """
        更新Lighter价格

        Args:
            bid: 买一价
            ask: 卖一价
            last: 最新成交价
        """
        self.lighter_prices['bid'] = bid
        self.lighter_prices['ask'] = ask

        # 如果有真实成交价，使用真实成交价
        if last:
            self.lighter_prices['last'] = last
        else:
            # 如果没有真实成交价，使用买卖价中间价
            if bid > 0 and ask > 0:
                mid_price = (bid + ask) / 2
                self.lighter_prices['last'] = mid_price
                logger.debug("使用买卖价中间价作为Lighter最新价",
                           bid=bid, ask=ask, mid_price=mid_price)

        logger.debug("Lighter价格更新", **self.lighter_prices)

    def analyze_spread(self) -> Optional[ArbitrageSignal]:
        """
        分析价差并生成交易信号

        Returns:
            套利信号，如果没有信号则返回None
        """
        # 检查价格数据完整性
        if not self._validate_prices():
            return None

        # 使用最新成交价计算价差
        binance_price = self.binance_prices.get('last', 0)
        lighter_price = self.lighter_prices.get('last', 0)

        if binance_price <= 0 or lighter_price <= 0:
            return None

        # 更新价差分析器
        spread_data = self.spread_analyzer.update_spread(binance_price, lighter_price)

        if not spread_data or spread_data.get('ma') is None:
            return None

        diff_rate = spread_data['diff_rate']
        ma_value = spread_data['ma']

        # 生成交易信号
        signal = self._generate_signal(diff_rate, ma_value, binance_price, lighter_price)

        if signal:
            self.current_signal = signal
            logger.info("生成套利信号", signal=signal)

        return signal

    def _validate_prices(self) -> bool:
        """验证价格数据完整性"""
        binance_valid = all(self.binance_prices[key] > 0 for key in ['bid', 'ask'])
        lighter_valid = all(self.lighter_prices[key] > 0 for key in ['bid', 'ask'])
        return binance_valid and lighter_valid

    def _generate_signal(self, diff_rate: float, ma_value: float,
                        binance_price: float, lighter_price: float) -> Optional[ArbitrageSignal]:
        """
        生成交易信号

        Args:
            diff_rate: 价差比例
            ma_value: 移动平均线值
            binance_price: Binance价格
            lighter_price: Lighter价格

        Returns:
            套利信号
        """
        # 检查价差是否超过最大阈值
        if abs(diff_rate) > self.max_spread_threshold:
            logger.warning("价差超过最大阈值", diff_rate=diff_rate, threshold=self.max_spread_threshold)
            return None

        # 计算信号强度
        signal_strength = abs(diff_rate - ma_value)

        # 检查是否满足最小盈利阈值
        if signal_strength < self.min_profit_threshold:
            return None

        # 确定信号类型
        if diff_rate < ma_value:
            # Binance相对便宜，在Binance买入，在Lighter卖出
            signal_type = SignalType.BUY
            reason = f"Binance价格低于MA {signal_strength:.4f}"
        elif diff_rate > ma_value:
            # Binance相对昂贵，在Binance卖出，在Lighter买入
            signal_type = SignalType.SELL
            reason = f"Binance价格高于MA {signal_strength:.4f}"
        else:
            signal_type = SignalType.HOLD
            reason = "价差接近MA"

        # 计算信心度
        confidence = min(signal_strength / self.min_profit_threshold, 1.0)

        # 计算预期利润和价差百分比
        spread_pct = abs(diff_rate) * 100  # 转换为百分比

        # 估算预期利润（基于最小交易量）
        if signal_type == SignalType.BUY:
            # Binance买入，Lighter卖出
            expected_profit = (lighter_price - binance_price) * self.min_trade_amount
        elif signal_type == SignalType.SELL:
            # Binance卖出，Lighter买入
            expected_profit = (binance_price - lighter_price) * self.min_trade_amount
        else:
            expected_profit = 0.0

        return ArbitrageSignal(
            signal_type=signal_type,
            binance_price=binance_price,
            lighter_price=lighter_price,
            diff_rate=diff_rate,
            ma_value=ma_value,
            confidence=confidence,
            timestamp=time.time(),
            reason=reason,
            expected_profit=expected_profit,
            spread_pct=spread_pct
        )

    def clear_expired_orders(self):
        """清理过期订单"""
        current_time = time.time()
        expired_orders = []

        for exchange, order_info in self.active_orders.items():
            # 检查订单是否过期（超过5分钟）
            if current_time - order_info.timestamp > 300:
                expired_orders.append(exchange)
                logger.info(f"发现过期订单: {exchange}, 订单时间: {order_info.timestamp}")

        for exchange in expired_orders:
            logger.info(f"清理过期订单: {exchange}")
            del self.active_orders[exchange]

    def should_place_order(self, signal: ArbitrageSignal) -> Tuple[bool, str]:
        """
        判断是否应该下单

        Args:
            signal: 套利信号

        Returns:
            (是否下单, 原因)
        """
        # 首先清理过期订单
        self.clear_expired_orders()

        # 检查是否正在对冲
        if self.is_hedging:
            return False, "正在对冲中"

        # 检查是否有活跃订单（改进后的检查）
        if self.active_orders:
            logger.debug("活跃订单详情", active_orders=self.active_orders)
            # 如果有活跃订单，检查是否可以进行对冲
            for exchange, order_info in self.active_orders.items():
                if order_info.status == 'filled':
                    # 如果订单已成交，可以进行对冲
                    logger.info(f"检测到已成交订单，可以对冲: {exchange}")
                    return True, f"可以对冲{exchange}的已成交订单"
            return False, "存在未成交的活跃订单"

        # 检查信号类型
        if signal.signal_type == SignalType.HOLD:
            return False, "信号为持有"

        # 检查信心度（降低阈值）
        if signal.confidence < 0.3:
            return False, f"信心度不足: {signal.confidence:.2f}"

        # 检查时间间隔（减少冷却时间）
        current_time = time.time()
        cooldown_period = self.config.get('strategy', {}).get('parameters', {}).get('cooldown_period', 10)
        if current_time - self.last_trade_time < cooldown_period:
            remaining_time = cooldown_period - (current_time - self.last_trade_time)
            return False, f"交易冷却时间未到，剩余{remaining_time:.1f}秒"

        return True, "满足下单条件"

    def calculate_order_amount(self, signal: ArbitrageSignal,
                             binance_bid_amount: float, lighter_bid_amount: float,
                             max_position: float) -> float:
        """
        计算订单数量

        Args:
            signal: 套利信号
            binance_bid_amount: Binance买一量
            lighter_bid_amount: Lighter买一量
            max_position: 最大持仓量

        Returns:
            订单数量
        """
        # 基础数量限制
        base_amount = min(self.max_trade_amount, max_position)

        # 考虑订单簿深度
        if signal.signal_type == SignalType.BUY:
            # 在Binance买入，限制为Binance买一量
            depth_limit = binance_bid_amount
        else:
            # 在Binance卖出，限制为Binance卖一量（这里用买一量作为近似）
            depth_limit = binance_bid_amount

        # 考虑对手盘深度
        counterpart_limit = lighter_bid_amount

        # 取最小值
        amount = min(base_amount, depth_limit, counterpart_limit)

        # 确保不小于最小交易量
        if amount < self.min_trade_amount:
            return 0

        return round(amount, 6)  # 保留6位小数

    def should_cancel_order(self, exchange: str, order_price: float) -> Tuple[bool, str]:
        """
        判断是否应该取消订单

        Args:
            exchange: 交易所名称
            order_price: 订单价格

        Returns:
            (是否取消, 原因)
        """
        if exchange not in self.active_orders:
            return False, "无活跃订单"

        order_info = self.active_orders[exchange]

        # 检查订单是否还在买一价
        if exchange == 'binance':
            current_best_price = self.binance_prices['bid'] if order_info.side == 'buy' else self.binance_prices['ask']
        else:
            current_best_price = self.lighter_prices['bid'] if order_info.side == 'buy' else self.lighter_prices['ask']

        if abs(order_price - current_best_price) > 0.0001:  # 价格偏差阈值
            return True, "订单价格不再是最优价格"

        # 检查信号是否仍然有效
        if self.current_signal:
            # 重新计算当前价差
            binance_price = self.binance_prices.get('last', 0)
            lighter_price = self.lighter_prices.get('last', 0)

            if binance_price > 0 and lighter_price > 0:
                current_diff_rate = (binance_price / lighter_price) - 1
                ma_value = self.spread_analyzer.spread_ma.get_current()

                if ma_value is not None:
                    # 检查信号方向是否仍然正确
                    if order_info.side == 'buy' and current_diff_rate >= ma_value:
                        return True, "买入信号失效"
                    elif order_info.side == 'sell' and current_diff_rate <= ma_value:
                        return True, "卖出信号失效"

        return False, "订单仍然有效"

    def add_active_order(self, exchange: str, order_info: OrderInfo) -> None:
        """添加活跃订单"""
        self.active_orders[exchange] = order_info
        logger.info("添加活跃订单", exchange=exchange, order_id=order_info.order_id)

    def remove_active_order(self, exchange: str) -> None:
        """移除活跃订单"""
        if exchange in self.active_orders:
            order_info = self.active_orders.pop(exchange)
            logger.info("移除活跃订单", exchange=exchange, order_id=order_info.order_id)

    def set_hedging_status(self, status: bool) -> None:
        """设置对冲状态"""
        self.is_hedging = status
        logger.info("对冲状态更新", is_hedging=status)

    def update_last_trade_time(self) -> None:
        """更新最后交易时间"""
        self.last_trade_time = time.time()

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            'symbol': self.symbol,
            'current_signal': self.current_signal,
            'active_orders': len(self.active_orders),
            'is_hedging': self.is_hedging,
            'last_trade_time': self.last_trade_time,
            'binance_prices': self.binance_prices,
            'lighter_prices': self.lighter_prices,
            'spread_ma_ready': self.spread_analyzer.spread_ma.is_ready(),
            'spread_ma_value': self.spread_analyzer.spread_ma.get_current()
        }

    def get_active_orders(self) -> Dict[str, OrderInfo]:
        """
        获取活跃订单

        Returns:
            活跃订单字典，键为交易所名称，值为订单信息
        """
        return self.active_orders.copy()

    def has_active_orders(self) -> bool:
        """
        检查是否有活跃订单

        Returns:
            是否有活跃订单
        """
        return len(self.active_orders) > 0

    def clear_active_orders(self) -> None:
        """
        清空所有活跃订单
        """
        if self.active_orders:
            logger.info("清空活跃订单", count=len(self.active_orders))
            self.active_orders.clear()