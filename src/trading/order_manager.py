"""
扩展交易功能模块

提供完整的订单管理功能，包括下单、撤单、批量操作等
"""

import asyncio
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import structlog

logger = structlog.get_logger(__name__)


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


@dataclass
class OrderRequest:
    """订单请求"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    amount: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    client_order_id: Optional[str] = None


@dataclass
class Order:
    """订单对象"""
    id: str
    client_order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    amount: float
    price: Optional[float]
    stop_price: Optional[float]
    status: OrderStatus
    filled_amount: float
    remaining_amount: float
    average_price: float
    created_time: float
    updated_time: float
    exchange: str
    fees: float = 0.0
    error_message: Optional[str] = None


@dataclass
class TradeExecution:
    """交易执行记录"""
    id: str
    order_id: str
    symbol: str
    side: OrderSide
    amount: float
    price: float
    fee: float
    timestamp: float
    exchange: str


class OrderManager:
    """
    订单管理器

    功能：
    - 统一的订单接口
    - 订单状态跟踪
    - 批量订单管理
    - 订单超时处理
    - 交易执行记录
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_paper_trading = config.get('trading', {}).get('paper_trading', True)

        # 订单存储
        self.active_orders: Dict[str, Order] = {}
        self.completed_orders: Dict[str, Order] = {}
        self.trade_executions: List[TradeExecution] = []

        # 交易所客户端引用
        self.binance_client = None
        self.lighter_client = None

        # 配置参数
        self.order_timeout = config.get('trading', {}).get('order_timeout', 300)  # 5分钟
        self.max_active_orders = config.get('trading', {}).get('max_active_orders', 10)

        # 统计信息
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'cancelled_orders': 0,
            'rejected_orders': 0,
            'total_volume': 0.0,
            'total_fees': 0.0
        }

        # 监控任务
        self.monitoring_task = None
        self.is_running = False

        logger.info("订单管理器初始化完成",
                   is_paper_trading=self.is_paper_trading,
                   order_timeout=self.order_timeout,
                   max_active_orders=self.max_active_orders)

    def set_exchange_clients(self, binance_client, lighter_client):
        """设置交易所客户端"""
        self.binance_client = binance_client
        self.lighter_client = lighter_client
        logger.info("交易所客户端设置完成")

    async def start(self):
        """启动订单管理器"""
        if self.is_running:
            return

        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitor_orders())
        logger.info("🚀 订单管理器启动")

    async def stop(self):
        """停止订单管理器"""
        self.is_running = False

        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        # 取消所有活跃订单
        await self.cancel_all_orders()

        logger.info("🛑 订单管理器停止")

    async def place_order(self, request: OrderRequest, exchange: str = "binance") -> Order:
        """
        下单

        Args:
            request: 订单请求
            exchange: 交易所名称

        Returns:
            订单对象
        """
        try:
            # 检查活跃订单数量限制
            if len(self.active_orders) >= self.max_active_orders:
                raise ValueError(f"活跃订单数量已达上限: {self.max_active_orders}")

            # 生成订单ID
            order_id = str(uuid.uuid4())
            client_order_id = request.client_order_id or f"order_{int(time.time() * 1000)}"

            # 创建订单对象
            order = Order(
                id=order_id,
                client_order_id=client_order_id,
                symbol=request.symbol,
                side=request.side,
                order_type=request.order_type,
                amount=request.amount,
                price=request.price,
                stop_price=request.stop_price,
                status=OrderStatus.PENDING,
                filled_amount=0.0,
                remaining_amount=request.amount,
                average_price=0.0,
                created_time=time.time(),
                updated_time=time.time(),
                exchange=exchange
            )

            # 模拟交易处理
            if self.is_paper_trading:
                order = await self._simulate_order_execution(order)
            else:
                # 真实交易处理
                order = await self._execute_real_order(order, exchange)

            # 存储订单
            if order.status in [OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]:
                self.active_orders[order.id] = order
            else:
                self.completed_orders[order.id] = order

            # 更新统计
            self.stats['total_orders'] += 1
            if order.status == OrderStatus.FILLED:
                self.stats['successful_orders'] += 1
                self.stats['total_volume'] += order.filled_amount
                self.stats['total_fees'] += order.fees

            logger.info("订单创建成功",
                       order_id=order.id,
                       symbol=order.symbol,
                       side=order.side.value,
                       amount=order.amount,
                       status=order.status.value,
                       exchange=exchange)

            return order

        except Exception as e:
            logger.error("下单失败", error=str(e), request=asdict(request))

            # 创建失败订单记录
            failed_order = Order(
                id=str(uuid.uuid4()),
                client_order_id=request.client_order_id or "failed",
                symbol=request.symbol,
                side=request.side,
                order_type=request.order_type,
                amount=request.amount,
                price=request.price,
                stop_price=request.stop_price,
                status=OrderStatus.REJECTED,
                filled_amount=0.0,
                remaining_amount=request.amount,
                average_price=0.0,
                created_time=time.time(),
                updated_time=time.time(),
                exchange=exchange,
                error_message=str(e)
            )

            self.completed_orders[failed_order.id] = failed_order
            self.stats['total_orders'] += 1
            self.stats['rejected_orders'] += 1

            return failed_order

    async def cancel_order(self, order_id: str) -> bool:
        """
        撤单

        Args:
            order_id: 订单ID

        Returns:
            是否成功撤销
        """
        try:
            if order_id not in self.active_orders:
                logger.warning("订单不存在或已完成", order_id=order_id)
                return False

            order = self.active_orders[order_id]

            # 模拟交易处理
            if self.is_paper_trading:
                success = await self._simulate_order_cancellation(order)
            else:
                # 真实交易处理
                success = await self._cancel_real_order(order)

            if success:
                order.status = OrderStatus.CANCELLED
                order.updated_time = time.time()

                # 移动到已完成订单
                self.completed_orders[order_id] = order
                del self.active_orders[order_id]

                self.stats['cancelled_orders'] += 1

                logger.info("订单撤销成功", order_id=order_id)
                return True
            else:
                logger.error("订单撤销失败", order_id=order_id)
                return False

        except Exception as e:
            logger.error("撤单操作失败", order_id=order_id, error=str(e))
            return False

    async def cancel_all_orders(self) -> int:
        """
        撤销所有活跃订单

        Returns:
            成功撤销的订单数量
        """
        try:
            cancelled_count = 0
            order_ids = list(self.active_orders.keys())

            for order_id in order_ids:
                if await self.cancel_order(order_id):
                    cancelled_count += 1

            logger.info("批量撤单完成", cancelled_count=cancelled_count, total_orders=len(order_ids))
            return cancelled_count

        except Exception as e:
            logger.error("批量撤单失败", error=str(e))
            return 0

    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        if order_id in self.active_orders:
            return self.active_orders[order_id]
        elif order_id in self.completed_orders:
            return self.completed_orders[order_id]
        else:
            return None

    def get_active_orders(self) -> List[Order]:
        """获取所有活跃订单"""
        return list(self.active_orders.values())

    def get_completed_orders(self, limit: int = 100) -> List[Order]:
        """获取已完成订单"""
        orders = list(self.completed_orders.values())
        orders.sort(key=lambda x: x.updated_time, reverse=True)
        return orders[:limit]

    def get_trade_executions(self, limit: int = 100) -> List[TradeExecution]:
        """获取交易执行记录"""
        executions = sorted(self.trade_executions, key=lambda x: x.timestamp, reverse=True)
        return executions[:limit]

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'active_orders_count': len(self.active_orders),
            'completed_orders_count': len(self.completed_orders),
            'trade_executions_count': len(self.trade_executions),
            'success_rate': (self.stats['successful_orders'] / max(self.stats['total_orders'], 1)) * 100
        }

    async def _simulate_order_execution(self, order: Order) -> Order:
        """模拟订单执行"""
        try:
            # 模拟网络延迟
            await asyncio.sleep(0.1)

            # 模拟订单提交
            order.status = OrderStatus.SUBMITTED
            order.updated_time = time.time()

            # 模拟部分成交或完全成交
            if order.order_type == OrderType.MARKET:
                # 市价单立即成交
                order.status = OrderStatus.FILLED
                order.filled_amount = order.amount
                order.remaining_amount = 0.0
                order.average_price = order.price or self._get_market_price(order.symbol, order.side)

                # 模拟手续费
                order.fees = order.filled_amount * order.average_price * 0.001  # 0.1% 手续费

                # 记录交易执行
                execution = TradeExecution(
                    id=str(uuid.uuid4()),
                    order_id=order.id,
                    symbol=order.symbol,
                    side=order.side,
                    amount=order.filled_amount,
                    price=order.average_price,
                    fee=order.fees,
                    timestamp=time.time(),
                    exchange=order.exchange
                )
                self.trade_executions.append(execution)

            else:
                # 限价单可能部分成交
                fill_probability = 0.8  # 80% 概率成交
                if time.time() % 1 < fill_probability:
                    order.status = OrderStatus.FILLED
                    order.filled_amount = order.amount
                    order.remaining_amount = 0.0
                    order.average_price = order.price
                    order.fees = order.filled_amount * order.average_price * 0.001

                    # 记录交易执行
                    execution = TradeExecution(
                        id=str(uuid.uuid4()),
                        order_id=order.id,
                        symbol=order.symbol,
                        side=order.side,
                        amount=order.filled_amount,
                        price=order.average_price,
                        fee=order.fees,
                        timestamp=time.time(),
                        exchange=order.exchange
                    )
                    self.trade_executions.append(execution)
                else:
                    # 订单提交但未成交
                    order.status = OrderStatus.SUBMITTED

            order.updated_time = time.time()

            logger.info("模拟订单执行完成",
                       order_id=order.id,
                       status=order.status.value,
                       filled_amount=order.filled_amount)

            return order

        except Exception as e:
            logger.error("模拟订单执行失败", order_id=order.id, error=str(e))
            order.status = OrderStatus.REJECTED
            order.error_message = str(e)
            return order

    async def _execute_real_order(self, order: Order, exchange: str) -> Order:
        """执行真实订单"""
        try:
            if exchange.lower() == "binance" and self.binance_client:
                # Binance订单执行
                result = await self.binance_client.place_order(
                    symbol=order.symbol,
                    side=order.side.value,
                    amount=order.amount,
                    price=order.price,
                    order_type=order.order_type.value
                )

                # 更新订单信息
                order.id = result.get('orderId', order.id)
                order.status = OrderStatus.SUBMITTED
                order.updated_time = time.time()

            elif exchange.lower() == "lighter" and self.lighter_client:
                # Lighter订单执行 (需要实现)
                logger.warning("Lighter真实交易暂未实现，使用模拟模式")
                return await self._simulate_order_execution(order)
            else:
                raise ValueError(f"不支持的交易所: {exchange}")

            return order

        except Exception as e:
            logger.error("真实订单执行失败", order_id=order.id, error=str(e))
            order.status = OrderStatus.REJECTED
            order.error_message = str(e)
            return order

    async def _simulate_order_cancellation(self, order: Order) -> bool:
        """模拟订单撤销"""
        try:
            # 模拟网络延迟
            await asyncio.sleep(0.05)

            # 模拟撤销成功率
            cancel_probability = 0.95  # 95% 概率撤销成功
            if time.time() % 1 < cancel_probability:
                logger.info("模拟订单撤销成功", order_id=order.id)
                return True
            else:
                logger.warning("模拟订单撤销失败（订单可能已成交）", order_id=order.id)
                return False

        except Exception as e:
            logger.error("模拟订单撤销失败", order_id=order.id, error=str(e))
            return False

    async def _cancel_real_order(self, order: Order) -> bool:
        """撤销真实订单"""
        try:
            if order.exchange.lower() == "binance" and self.binance_client:
                # Binance订单撤销
                result = await self.binance_client.cancel_order(
                    symbol=order.symbol,
                    order_id=order.id
                )
                return result.get('status') == 'CANCELED'

            elif order.exchange.lower() == "lighter" and self.lighter_client:
                # Lighter订单撤销 (需要实现)
                logger.warning("Lighter真实撤单暂未实现，使用模拟模式")
                return await self._simulate_order_cancellation(order)
            else:
                logger.error("不支持的交易所", exchange=order.exchange)
                return False

        except Exception as e:
            logger.error("真实订单撤销失败", order_id=order.id, error=str(e))
            return False

    def _get_market_price(self, symbol: str, side: OrderSide) -> float:
        """获取市场价格（用于模拟交易）"""
        try:
            # 简化的价格模拟
            base_price = 50000.0  # BTC基础价格

            if symbol.startswith("BTC"):
                base_price = 50000.0
            elif symbol.startswith("ETH"):
                base_price = 3000.0
            else:
                base_price = 1.0

            # 添加随机波动
            import random
            volatility = 0.001  # 0.1% 波动
            price_change = random.uniform(-volatility, volatility)

            market_price = base_price * (1 + price_change)

            # 买单价格稍高，卖单价格稍低（模拟滑点）
            if side == OrderSide.BUY:
                return market_price * 1.0005  # 0.05% 滑点
            else:
                return market_price * 0.9995

        except Exception as e:
            logger.error("获取市场价格失败", error=str(e))
            return 50000.0  # 默认价格

    async def _monitor_orders(self):
        """监控订单状态"""
        while self.is_running:
            try:
                current_time = time.time()
                expired_orders = []

                # 检查订单超时
                for order_id, order in self.active_orders.items():
                    if current_time - order.created_time > self.order_timeout:
                        expired_orders.append(order_id)

                # 处理超时订单
                for order_id in expired_orders:
                    order = self.active_orders[order_id]
                    logger.warning("订单超时，自动撤销", order_id=order_id)

                    if await self.cancel_order(order_id):
                        order.status = OrderStatus.EXPIRED
                    else:
                        # 撤销失败，可能已经成交
                        await self._check_order_status(order)

                # 检查部分成交订单的状态更新
                for order in list(self.active_orders.values()):
                    if order.status == OrderStatus.SUBMITTED:
                        await self._check_order_status(order)

                # 清理过期的交易执行记录
                cutoff_time = current_time - (24 * 3600)  # 保留24小时
                self.trade_executions = [
                    exec for exec in self.trade_executions
                    if exec.timestamp > cutoff_time
                ]

            except Exception as e:
                logger.error("订单监控失败", error=str(e))

            await asyncio.sleep(30)  # 每30秒检查一次

    async def _check_order_status(self, order: Order):
        """检查订单状态更新"""
        try:
            if self.is_paper_trading:
                # 模拟订单状态更新
                if order.status == OrderStatus.SUBMITTED:
                    # 随机模拟订单成交
                    if time.time() % 10 < 2:  # 20% 概率成交
                        order.status = OrderStatus.FILLED
                        order.filled_amount = order.amount
                        order.remaining_amount = 0.0
                        order.average_price = order.price or self._get_market_price(order.symbol, order.side)
                        order.fees = order.filled_amount * order.average_price * 0.001
                        order.updated_time = time.time()

                        # 移动到已完成订单
                        self.completed_orders[order.id] = order
                        del self.active_orders[order.id]

                        self.stats['successful_orders'] += 1
                        self.stats['total_volume'] += order.filled_amount
                        self.stats['total_fees'] += order.fees

                        logger.info("模拟订单成交", order_id=order.id)
            else:
                # 真实订单状态查询
                # TODO: 实现真实的订单状态查询
                pass

        except Exception as e:
            logger.error("检查订单状态失败", order_id=order.id, error=str(e))
