"""
日志系统

提供结构化日志记录功能
"""

import sys
import logging
from pathlib import Path
from typing import Optional
import structlog
from structlog.stdlib import LoggerFactory

try:
    import colorlog
except ImportError:
    colorlog = None


def setup_logger(name: str = __name__, level: str = "INFO") -> structlog.BoundLogger:
    """
    设置并返回一个structlog logger
    
    Args:
        name: logger名称
        level: 日志级别
        
    Returns:
        配置好的structlog logger
    """
    # 配置基础logging
    logging.basicConfig(
        level=getattr(logging, level.upper(), logging.INFO),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger(name)


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    console_logging: bool = True,
    colored_logs: bool = True,
    max_file_size: str = "10MB",
    backup_count: int = 5
) -> None:
    """
    设置日志系统
    
    Args:
        level: 日志级别
        log_file: 日志文件路径
        console_logging: 是否输出到控制台
        colored_logs: 是否使用彩色日志
        max_file_size: 单个日志文件最大大小
        backup_count: 备份文件数量
    """
    # 配置标准库logging
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(message)s",
        handlers=[]
    )
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # 获取根logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 控制台处理器
    if console_logging:
        console_handler = logging.StreamHandler(sys.stdout)
        
        if colored_logs and colorlog:
            console_formatter = colorlog.ColoredFormatter(
                '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S',
                log_colors={
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                }
            )
        else:
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        from logging.handlers import RotatingFileHandler
        
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 解析文件大小
        if max_file_size.endswith('MB'):
            max_bytes = int(max_file_size[:-2]) * 1024 * 1024
        else:
            max_bytes = 10 * 1024 * 1024  # 默认10MB
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)


class TradingLogger:
    """交易专用日志记录器"""
    
    def __init__(self, name: str = "arbitrage"):
        """
        初始化交易日志记录器
        
        Args:
            name: 日志记录器名称
        """
        self.logger = structlog.get_logger(name)
    
    def trade_executed(self, exchange: str, side: str, amount: float, 
                      price: float, symbol: str, order_id: str = None) -> None:
        """记录交易执行"""
        self.logger.info(
            "交易执行",
            exchange=exchange,
            side=side,
            amount=amount,
            price=price,
            symbol=symbol,
            order_id=order_id,
            event_type="trade_executed"
        )
    
    def order_placed(self, exchange: str, side: str, amount: float,
                    price: float, symbol: str, order_id: str) -> None:
        """记录订单下单"""
        self.logger.info(
            "订单下单",
            exchange=exchange,
            side=side,
            amount=amount,
            price=price,
            symbol=symbol,
            order_id=order_id,
            event_type="order_placed"
        )
    
    def order_cancelled(self, exchange: str, order_id: str, reason: str = None) -> None:
        """记录订单取消"""
        self.logger.info(
            "订单取消",
            exchange=exchange,
            order_id=order_id,
            reason=reason,
            event_type="order_cancelled"
        )
    
    def arbitrage_opportunity(self, buy_exchange: str, sell_exchange: str,
                            spread: float, profit: float) -> None:
        """记录套利机会"""
        self.logger.info(
            "套利机会发现",
            buy_exchange=buy_exchange,
            sell_exchange=sell_exchange,
            spread=spread,
            profit=profit,
            event_type="arbitrage_opportunity"
        )
    
    def risk_alert(self, risk_type: str, current_value: float,
                  threshold: float, description: str) -> None:
        """记录风险告警"""
        self.logger.warning(
            "风险告警",
            risk_type=risk_type,
            current_value=current_value,
            threshold=threshold,
            description=description,
            event_type="risk_alert"
        )
    
    def error(self, error_type: str, error_msg: str, **kwargs) -> None:
        """记录错误"""
        self.logger.error(
            "系统错误",
            error_type=error_type,
            error_msg=error_msg,
            event_type="error",
            **kwargs
        )
    
    def performance_metric(self, metric_name: str, value: float,
                          unit: str = None, **kwargs) -> None:
        """记录性能指标"""
        self.logger.info(
            "性能指标",
            metric_name=metric_name,
            value=value,
            unit=unit,
            event_type="performance_metric",
            **kwargs
        )


# 创建全局交易日志记录器
trading_logger = TradingLogger() 