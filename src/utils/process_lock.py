"""
进程锁管理器

防止多个系统实例同时运行
"""

import os
import sys
import time
import signal
import atexit
import psutil
import structlog
from pathlib import Path
from typing import Optional

logger = structlog.get_logger(__name__)


class ProcessLock:
    """进程锁管理器"""
    
    def __init__(self, lock_file: str = "data/arbitrage.lock", timeout: int = 10):
        """
        初始化进程锁
        
        Args:
            lock_file: 锁文件路径
            timeout: 超时时间（秒）
        """
        self.lock_file = Path(lock_file)
        self.timeout = timeout
        self.pid = os.getpid()
        self.acquired = False
        
        # 确保目录存在
        self.lock_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 注册清理函数
        atexit.register(self.release)
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def acquire(self) -> bool:
        """
        获取进程锁
        
        Returns:
            是否成功获取锁
        """
        try:
            # 检查是否已经有实例在运行
            if self._is_already_running():
                return False
            
            # 创建锁文件
            self._create_lock_file()
            self.acquired = True
            
            logger.info("进程锁获取成功", pid=self.pid, lock_file=str(self.lock_file))
            return True
            
        except Exception as e:
            logger.error("获取进程锁失败", error=str(e))
            return False
    
    def _is_already_running(self) -> bool:
        """检查是否已经有实例在运行"""
        if not self.lock_file.exists():
            return False
        
        try:
            # 读取锁文件中的PID
            with open(self.lock_file, 'r') as f:
                content = f.read().strip()
                if not content:
                    # 空文件，清理它
                    self.lock_file.unlink()
                    return False
                
                data = content.split('\n')
                if len(data) < 2:
                    # 格式错误，清理文件
                    self.lock_file.unlink()
                    return False
                
                old_pid = int(data[0])
                start_time = float(data[1])
            
            # 检查进程是否还在运行
            if self._is_process_running(old_pid, start_time):
                # 进程还在运行
                process_info = self._get_process_info(old_pid)
                logger.warning(
                    "检测到已有实例正在运行",
                    running_pid=old_pid,
                    current_pid=self.pid,
                    process_info=process_info,
                    lock_file=str(self.lock_file)
                )
                
                # 显示友好的错误信息
                print(f"⚠️  检测到套利系统已经在运行!")
                print(f"   运行中的进程 PID: {old_pid}")
                if process_info:
                    print(f"   进程信息: {process_info}")
                print(f"   锁文件: {self.lock_file}")
                print(f"\n💡 解决方案:")
                print(f"   1. 如果要停止现有实例: kill {old_pid}")
                print(f"   2. 或者手动删除锁文件: rm {self.lock_file}")
                print(f"   3. 或者等待现有实例自然结束")
                
                return True
            else:
                # 进程已经不存在，清理陈旧的锁文件
                logger.info("清理陈旧的锁文件", old_pid=old_pid)
                self.lock_file.unlink()
                return False
                
        except (ValueError, IOError, OSError) as e:
            # 锁文件损坏或读取失败，清理它
            logger.warning("锁文件损坏，自动清理", error=str(e))
            try:
                self.lock_file.unlink()
            except:
                pass
            return False
    
    def _is_process_running(self, pid: int, start_time: float) -> bool:
        """检查指定PID的进程是否还在运行"""
        try:
            process = psutil.Process(pid)
            
            # 检查进程是否存在且是我们的进程
            if process.is_running():
                # 验证进程启动时间，避免PID重用的问题
                process_start_time = process.create_time()
                time_diff = abs(process_start_time - start_time)
                
                # 如果启动时间差异在合理范围内（10秒），认为是同一个进程
                if time_diff < 10:
                    return True
                else:
                    logger.info("检测到PID重用，进程不匹配", 
                              pid=pid, 
                              recorded_start_time=start_time,
                              actual_start_time=process_start_time)
                    return False
            else:
                return False
                
        except psutil.NoSuchProcess:
            return False
        except Exception as e:
            logger.warning("检查进程状态失败", pid=pid, error=str(e))
            return False
    
    def _get_process_info(self, pid: int) -> Optional[str]:
        """获取进程信息"""
        try:
            process = psutil.Process(pid)
            cmdline = ' '.join(process.cmdline())
            return f"{process.name()} - {cmdline}"
        except Exception:
            return None
    
    def _create_lock_file(self) -> None:
        """创建锁文件"""
        try:
            # 获取当前进程的启动时间
            current_process = psutil.Process(self.pid)
            start_time = current_process.create_time()
            
            # 写入PID和启动时间
            with open(self.lock_file, 'w') as f:
                f.write(f"{self.pid}\n{start_time}\n{time.time()}")
                
        except Exception as e:
            logger.error("创建锁文件失败", error=str(e))
            raise
    
    def release(self) -> None:
        """释放进程锁"""
        if self.acquired and self.lock_file.exists():
            try:
                # 验证锁文件是否属于当前进程
                with open(self.lock_file, 'r') as f:
                    content = f.read().strip()
                    if content:
                        data = content.split('\n')
                        if len(data) >= 1:
                            file_pid = int(data[0])
                            if file_pid == self.pid:
                                self.lock_file.unlink()
                                logger.info("进程锁释放成功", pid=self.pid)
                            else:
                                logger.warning("锁文件PID不匹配，跳过删除", 
                                             file_pid=file_pid, current_pid=self.pid)
                        else:
                            # 文件内容异常，强制删除
                            self.lock_file.unlink()
                            logger.info("清理异常锁文件", pid=self.pid)
                            
            except Exception as e:
                logger.error("释放进程锁失败", error=str(e))
                
            self.acquired = False
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("收到退出信号，清理进程锁", signal=signum, pid=self.pid)
        self.release()
        sys.exit(0)
    
    def __enter__(self):
        """上下文管理器入口"""
        if self.acquire():
            return self
        else:
            raise RuntimeError("无法获取进程锁，可能已有实例在运行")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()


def check_single_instance(lock_file: str = "data/arbitrage.lock") -> ProcessLock:
    """
    检查单实例运行
    
    Args:
        lock_file: 锁文件路径
        
    Returns:
        进程锁对象
        
    Raises:
        RuntimeError: 如果已有实例在运行
    """
    process_lock = ProcessLock(lock_file)
    
    if not process_lock.acquire():
        raise RuntimeError("无法启动：已有套利系统实例在运行")
    
    return process_lock


def force_cleanup_lock(lock_file: str = "data/arbitrage.lock") -> bool:
    """
    强制清理锁文件
    
    Args:
        lock_file: 锁文件路径
        
    Returns:
        是否成功清理
    """
    try:
        lock_path = Path(lock_file)
        if lock_path.exists():
            lock_path.unlink()
            logger.info("强制清理锁文件成功", lock_file=lock_file)
            return True
        else:
            logger.info("锁文件不存在", lock_file=lock_file)
            return True
    except Exception as e:
        logger.error("强制清理锁文件失败", lock_file=lock_file, error=str(e))
        return False


def get_running_instance_info(lock_file: str = "data/arbitrage.lock") -> Optional[dict]:
    """
    获取运行中实例的信息
    
    Args:
        lock_file: 锁文件路径
        
    Returns:
        实例信息字典或None
    """
    try:
        lock_path = Path(lock_file)
        if not lock_path.exists():
            return None
        
        with open(lock_path, 'r') as f:
            content = f.read().strip()
            if not content:
                return None
            
            data = content.split('\n')
            if len(data) < 2:
                return None
            
            pid = int(data[0])
            start_time = float(data[1])
        
        # 检查进程是否在运行
        try:
            process = psutil.Process(pid)
            if process.is_running():
                return {
                    'pid': pid,
                    'start_time': start_time,
                    'name': process.name(),
                    'cmdline': ' '.join(process.cmdline()),
                    'cpu_percent': process.cpu_percent(),
                    'memory_percent': process.memory_percent(),
                    'status': process.status()
                }
        except psutil.NoSuchProcess:
            pass
        
        return None
        
    except Exception as e:
        logger.error("获取运行实例信息失败", error=str(e))
        return None 