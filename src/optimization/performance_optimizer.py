"""
性能优化模块

提供系统性能监控、分析和自动优化功能
"""

import asyncio
import time
import gc
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import structlog

logger = structlog.get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    memory_available: float
    gc_collections: Dict[str, int]
    thread_count: int
    active_connections: int
    request_latency: float
    throughput: float
    error_rate: float


@dataclass
class OptimizationAction:
    """优化动作"""
    name: str
    description: str
    action_type: str  # 'gc', 'cache', 'connection', 'thread'
    parameters: Dict[str, Any]
    priority: int  # 1-10, 10最高
    enabled: bool = True


class PerformanceOptimizer:
    """
    性能优化器
    
    功能：
    - 实时性能监控
    - 自动性能优化
    - 资源管理
    - 缓存优化
    - 连接池管理
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_running = False
        
        # 性能数据存储
        self.metrics_history: deque = deque(maxlen=1000)  # 保留最近1000个数据点
        self.optimization_history: List[Dict] = []
        
        # 优化配置
        self.optimization_interval = config.get('optimization', {}).get('interval', 60)  # 60秒
        self.auto_optimization = config.get('optimization', {}).get('auto_enabled', True)
        
        # 性能阈值
        self.thresholds = {
            'cpu_usage': config.get('optimization', {}).get('cpu_threshold', 80.0),
            'memory_usage': config.get('optimization', {}).get('memory_threshold', 85.0),
            'latency': config.get('optimization', {}).get('latency_threshold', 500.0),
            'error_rate': config.get('optimization', {}).get('error_rate_threshold', 5.0)
        }
        
        # 缓存管理
        self.cache_pools = {}
        self.cache_stats = defaultdict(lambda: {'hits': 0, 'misses': 0, 'size': 0})
        
        # 连接池管理
        self.connection_pools = {}
        self.connection_stats = defaultdict(lambda: {'active': 0, 'idle': 0, 'total': 0})
        
        # 优化动作定义
        self.optimization_actions = [
            OptimizationAction(
                name="强制垃圾回收",
                description="执行强制垃圾回收以释放内存",
                action_type="gc",
                parameters={'generation': 2},
                priority=8
            ),
            OptimizationAction(
                name="清理缓存",
                description="清理过期和低频使用的缓存",
                action_type="cache",
                parameters={'max_age': 3600, 'usage_threshold': 0.1},
                priority=6
            ),
            OptimizationAction(
                name="优化连接池",
                description="调整连接池大小和清理空闲连接",
                action_type="connection",
                parameters={'max_idle_time': 300},
                priority=5
            ),
            OptimizationAction(
                name="线程池优化",
                description="优化线程池配置",
                action_type="thread",
                parameters={'target_size': 10},
                priority=4
            )
        ]
        
        # 回调函数
        self.optimization_callbacks: List[Callable] = []
        
        logger.info("性能优化器初始化完成",
                   auto_optimization=self.auto_optimization,
                   optimization_interval=self.optimization_interval,
                   thresholds=self.thresholds)

    async def start(self):
        """启动性能优化器"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("🚀 启动性能优化器")
        
        # 启动监控和优化任务
        tasks = [
            asyncio.create_task(self._monitor_performance()),
            asyncio.create_task(self._optimization_loop()),
            asyncio.create_task(self._cleanup_task())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            logger.info("性能优化任务被取消")

    async def stop(self):
        """停止性能优化器"""
        self.is_running = False
        logger.info("🛑 停止性能优化器")

    def add_optimization_callback(self, callback: Callable):
        """添加优化回调函数"""
        self.optimization_callbacks.append(callback)

    async def _monitor_performance(self):
        """监控性能指标"""
        while self.is_running:
            try:
                import psutil
                
                # 获取系统指标
                cpu_usage = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # 获取垃圾回收统计
                gc_stats = {}
                for i in range(3):
                    gc_stats[f'gen_{i}'] = gc.get_count()[i]
                
                # 获取线程数
                thread_count = threading.active_count()
                
                # 计算请求延迟和吞吐量（简化版）
                request_latency = self._calculate_average_latency()
                throughput = self._calculate_throughput()
                error_rate = self._calculate_error_rate()
                
                # 创建性能指标
                metrics = PerformanceMetrics(
                    timestamp=time.time(),
                    cpu_usage=cpu_usage,
                    memory_usage=memory.percent,
                    memory_available=memory.available / (1024 * 1024 * 1024),  # GB
                    gc_collections=gc_stats,
                    thread_count=thread_count,
                    active_connections=self._count_active_connections(),
                    request_latency=request_latency,
                    throughput=throughput,
                    error_rate=error_rate
                )
                
                # 存储指标
                self.metrics_history.append(metrics)
                
                logger.debug("性能指标收集完成",
                           cpu=cpu_usage,
                           memory=memory.percent,
                           threads=thread_count,
                           latency=request_latency)
                
            except Exception as e:
                logger.error("性能监控失败", error=str(e))
            
            await asyncio.sleep(10)  # 每10秒收集一次

    async def _optimization_loop(self):
        """优化循环"""
        while self.is_running:
            try:
                if self.auto_optimization and self.metrics_history:
                    await self._analyze_and_optimize()
                
            except Exception as e:
                logger.error("性能优化失败", error=str(e))
            
            await asyncio.sleep(self.optimization_interval)

    async def _analyze_and_optimize(self):
        """分析性能并执行优化"""
        try:
            if not self.metrics_history:
                return
            
            latest_metrics = self.metrics_history[-1]
            
            # 检查是否需要优化
            optimization_needed = []
            
            if latest_metrics.cpu_usage > self.thresholds['cpu_usage']:
                optimization_needed.append('cpu')
            
            if latest_metrics.memory_usage > self.thresholds['memory_usage']:
                optimization_needed.append('memory')
            
            if latest_metrics.request_latency > self.thresholds['latency']:
                optimization_needed.append('latency')
            
            if latest_metrics.error_rate > self.thresholds['error_rate']:
                optimization_needed.append('error_rate')
            
            if not optimization_needed:
                return
            
            logger.info("检测到性能问题，开始优化",
                       issues=optimization_needed,
                       cpu=latest_metrics.cpu_usage,
                       memory=latest_metrics.memory_usage,
                       latency=latest_metrics.request_latency)
            
            # 执行优化动作
            for action in sorted(self.optimization_actions, key=lambda x: x.priority, reverse=True):
                if not action.enabled:
                    continue
                
                should_execute = False
                
                # 根据问题类型决定是否执行特定优化
                if 'memory' in optimization_needed and action.action_type in ['gc', 'cache']:
                    should_execute = True
                elif 'cpu' in optimization_needed and action.action_type in ['thread', 'connection']:
                    should_execute = True
                elif 'latency' in optimization_needed and action.action_type in ['connection', 'cache']:
                    should_execute = True
                
                if should_execute:
                    await self._execute_optimization_action(action)
            
        except Exception as e:
            logger.error("性能分析和优化失败", error=str(e))

    async def _execute_optimization_action(self, action: OptimizationAction):
        """执行优化动作"""
        try:
            start_time = time.time()
            
            if action.action_type == 'gc':
                await self._optimize_garbage_collection(action.parameters)
            elif action.action_type == 'cache':
                await self._optimize_cache(action.parameters)
            elif action.action_type == 'connection':
                await self._optimize_connections(action.parameters)
            elif action.action_type == 'thread':
                await self._optimize_threads(action.parameters)
            
            execution_time = time.time() - start_time
            
            # 记录优化历史
            optimization_record = {
                'timestamp': time.time(),
                'action': action.name,
                'type': action.action_type,
                'parameters': action.parameters,
                'execution_time': execution_time,
                'success': True
            }
            self.optimization_history.append(optimization_record)
            
            logger.info("优化动作执行完成",
                       action=action.name,
                       execution_time=execution_time)
            
            # 调用回调函数
            for callback in self.optimization_callbacks:
                try:
                    await callback(optimization_record)
                except Exception as e:
                    logger.error("优化回调执行失败", error=str(e))
            
        except Exception as e:
            logger.error("优化动作执行失败", action=action.name, error=str(e))
            
            # 记录失败
            optimization_record = {
                'timestamp': time.time(),
                'action': action.name,
                'type': action.action_type,
                'parameters': action.parameters,
                'execution_time': 0,
                'success': False,
                'error': str(e)
            }
            self.optimization_history.append(optimization_record)

    async def _optimize_garbage_collection(self, parameters: Dict[str, Any]):
        """优化垃圾回收"""
        generation = parameters.get('generation', 2)
        
        # 执行垃圾回收
        collected = gc.collect(generation)
        
        logger.info("执行垃圾回收",
                   generation=generation,
                   collected_objects=collected)

    async def _optimize_cache(self, parameters: Dict[str, Any]):
        """优化缓存"""
        max_age = parameters.get('max_age', 3600)
        usage_threshold = parameters.get('usage_threshold', 0.1)
        current_time = time.time()
        
        cleaned_count = 0
        
        for pool_name, cache_pool in self.cache_pools.items():
            if hasattr(cache_pool, 'cleanup'):
                # 清理过期缓存
                cleaned = cache_pool.cleanup(max_age=max_age, usage_threshold=usage_threshold)
                cleaned_count += cleaned
        
        logger.info("缓存清理完成", cleaned_items=cleaned_count)

    async def _optimize_connections(self, parameters: Dict[str, Any]):
        """优化连接池"""
        max_idle_time = parameters.get('max_idle_time', 300)
        
        cleaned_connections = 0
        
        for pool_name, connection_pool in self.connection_pools.items():
            if hasattr(connection_pool, 'cleanup_idle'):
                # 清理空闲连接
                cleaned = connection_pool.cleanup_idle(max_idle_time)
                cleaned_connections += cleaned
        
        logger.info("连接池优化完成", cleaned_connections=cleaned_connections)

    async def _optimize_threads(self, parameters: Dict[str, Any]):
        """优化线程池"""
        target_size = parameters.get('target_size', 10)
        current_threads = threading.active_count()
        
        # 简化的线程优化（实际实现需要更复杂的逻辑）
        logger.info("线程池状态检查",
                   current_threads=current_threads,
                   target_size=target_size)

    def _calculate_average_latency(self) -> float:
        """计算平均延迟"""
        # 简化实现，实际应该从请求统计中获取
        if len(self.metrics_history) >= 2:
            return sum(m.request_latency for m in list(self.metrics_history)[-10:]) / min(10, len(self.metrics_history))
        return 0.0

    def _calculate_throughput(self) -> float:
        """计算吞吐量"""
        # 简化实现，实际应该从请求统计中获取
        return 100.0  # 每秒请求数

    def _calculate_error_rate(self) -> float:
        """计算错误率"""
        # 简化实现，实际应该从错误统计中获取
        return 0.5  # 错误率百分比

    def _count_active_connections(self) -> int:
        """统计活跃连接数"""
        total_connections = 0
        for stats in self.connection_stats.values():
            total_connections += stats['active']
        return total_connections

    async def _cleanup_task(self):
        """清理任务"""
        while self.is_running:
            try:
                # 清理过期的优化历史记录
                cutoff_time = time.time() - (24 * 3600)  # 保留24小时
                self.optimization_history = [
                    record for record in self.optimization_history
                    if record['timestamp'] > cutoff_time
                ]
                
                logger.debug("清理优化历史记录",
                           remaining_records=len(self.optimization_history))
                
            except Exception as e:
                logger.error("清理任务失败", error=str(e))
            
            await asyncio.sleep(3600)  # 每小时清理一次

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            if not self.metrics_history:
                return {'error': '暂无性能数据'}
            
            recent_metrics = list(self.metrics_history)[-10:]  # 最近10个数据点
            
            summary = {
                'timestamp': time.time(),
                'metrics_count': len(self.metrics_history),
                'optimization_count': len(self.optimization_history),
                'current_performance': asdict(self.metrics_history[-1]),
                'average_performance': {
                    'cpu_usage': sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
                    'memory_usage': sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
                    'request_latency': sum(m.request_latency for m in recent_metrics) / len(recent_metrics),
                    'throughput': sum(m.throughput for m in recent_metrics) / len(recent_metrics),
                    'error_rate': sum(m.error_rate for m in recent_metrics) / len(recent_metrics)
                },
                'thresholds': self.thresholds,
                'recent_optimizations': self.optimization_history[-5:] if self.optimization_history else []
            }
            
            return summary
            
        except Exception as e:
            logger.error("获取性能摘要失败", error=str(e))
            return {'error': str(e)}
