"""
Binance-Lighter 套利交易系统主入口

这是一个高性能的加密货币套利交易系统，支持Binance和Lighter交易所之间的自动套利。
"""

import os
import sys
import signal
import socket
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any
import structlog
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from .utils.config_loader import ConfigLoader
from .utils.logger import setup_logging
from .arbitrage.engine import ArbitrageEngine
from .web.app import create_web_app

logger = structlog.get_logger(__name__)


def check_port_available(host: str, port: int) -> bool:
    """
    检查端口是否可用
    
    Args:
        host: 主机地址
        port: 端口号
        
    Returns:
        端口是否可用
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            result = sock.connect_ex((host, port))
            return result != 0  # 0表示连接成功，端口被占用
    except Exception:
        return False


def find_available_port(host: str, start_port: int, max_attempts: int = 10) -> Optional[int]:
    """
    寻找可用端口
    
    Args:
        host: 主机地址
        start_port: 起始端口
        max_attempts: 最大尝试次数
        
    Returns:
        可用端口号，如果找不到则返回None
    """
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(host, port):
            return port
    return None


class ArbitrageSystem:
    """套利交易系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.config_loader = ConfigLoader()
        self.config = None
        self.engine = None
        self.web_app = None
        self.web_server = None
        
    async def initialize(self) -> None:
        """初始化系统"""
        try:
            # 加载配置
            config_file = os.getenv("CONFIG_FILE", "config/settings.yaml")
            self.config = self.config_loader.load_config(config_file)
            
            # 处理环境变量覆盖
            self._apply_environment_overrides()
            
            # 验证配置
            if not self.config_loader.validate_config(self.config):
                raise ValueError("配置验证失败")
            
            # 设置日志
            log_config = self.config.get('logging', {})
            setup_logging(
                level=log_config.get('level', 'INFO'),
                log_file=log_config.get('log_file'),
                console_logging=log_config.get('console_logging', True),
                colored_logs=log_config.get('colored_logs', True),
                max_file_size=log_config.get('max_file_size', '10MB'),
                backup_count=log_config.get('backup_count', 5)
            )
            
            logger.info("系统配置加载完成")
            
            # 初始化套利引擎
            self.engine = ArbitrageEngine(self.config)
            await self.engine.initialize()
            
            # 初始化Web应用（如果未禁用）
            if not os.getenv("NO_WEB"):
                self.web_app = create_web_app(self.engine)
                logger.info("Web监控界面初始化完成")
            
            logger.info("套利交易系统初始化完成")
            
        except Exception as e:
            logger.error("系统初始化失败", error=str(e))
            raise
    
    def _apply_environment_overrides(self) -> None:
        """应用环境变量覆盖"""
        # 处理模拟交易模式
        if os.getenv("PAPER_TRADING"):
            if 'development' not in self.config:
                self.config['development'] = {}
            self.config['development']['paper_trading'] = True
            logger.info("环境变量覆盖：启用模拟交易模式")
        
        # 处理日志级别
        if os.getenv("LOG_LEVEL"):
            if 'logging' not in self.config:
                self.config['logging'] = {}
            self.config['logging']['level'] = os.getenv("LOG_LEVEL")
            logger.info(f"环境变量覆盖：日志级别 {os.getenv('LOG_LEVEL')}")
        
        # 处理Web配置
        if os.getenv("WEB_PORT"):
            if 'monitoring' not in self.config:
                self.config['monitoring'] = {}
            self.config['monitoring']['web_port'] = int(os.getenv("WEB_PORT"))
            logger.info(f"环境变量覆盖：Web端口 {os.getenv('WEB_PORT')}")
        
        if os.getenv("WEB_HOST"):
            if 'monitoring' not in self.config:
                self.config['monitoring'] = {}
            self.config['monitoring']['web_host'] = os.getenv("WEB_HOST")
            logger.info(f"环境变量覆盖：Web主机 {os.getenv('WEB_HOST')}")
        
        # 确保Web配置有默认值
        if 'monitoring' not in self.config:
            self.config['monitoring'] = {}
        if 'web_host' not in self.config['monitoring']:
            self.config['monitoring']['web_host'] = 'localhost'
        if 'web_port' not in self.config['monitoring']:
            self.config['monitoring']['web_port'] = 8000
    
    async def start_web_server(self):
        """启动Web监控服务器"""
        try:
            monitoring_config = self.config.get('monitoring', {})
            host = monitoring_config.get('web_host', 'localhost')
            port = int(monitoring_config.get('web_port', 8000))
            
            # 检查端口是否可用
            if not check_port_available(host, port):
                logger.warning("🚪 指定端口被占用，正在寻找可用端口", 
                             original_port=port, host=host)
                
                available_port = find_available_port(host, port)
                if available_port:
                    port = available_port
                    logger.info("✅ 找到可用端口", host=host, port=port)
                else:
                    logger.error("❌ 无法找到可用端口，Web服务器启动失败")
                    return
            
            # 创建uvicorn配置
            config = uvicorn.Config(
                self.web_app,
                host=host,
                port=port,
                log_level="info",
                access_log=False,  # 禁用访问日志减少噪音
                loop="asyncio"
            )
            
            # 创建服务器
            self.web_server = uvicorn.Server(config)
            
            logger.info("🌐 启动Web监控服务器", host=host, port=port)
            print(f"🌐 Web监控界面: http://{host}:{port}")
            
            # 启动服务器
            await self.web_server.serve()
            
        except Exception as e:
            logger.error("Web服务器启动失败", error=str(e))
            # 不要让Web服务器错误影响整个系统
            # raise
    
    async def start(self) -> None:
        """启动系统"""
        try:
            logger.info("正在启动套利交易系统...")
            
            # 初始化系统
            await self.initialize()
            
            # 启动Web服务器（如果配置了）
            web_task = None
            if self.web_app:
                web_task = asyncio.create_task(self.start_web_server())
            
            # 启动套利引擎
            engine_task = asyncio.create_task(self.engine.start())
            
            # 等待任务完成
            if web_task:
                await asyncio.gather(engine_task, web_task)
            else:
                await engine_task
            
        except Exception as e:
            logger.error("系统启动失败", error=str(e))
            raise
    
    async def stop(self):
        """停止系统"""
        try:
            logger.info("⏹️ 正在停止套利交易系统...")
            
            # 设置停止标志
            self.is_running = False
            
            # 停止套利引擎
            if self.engine:
                await self.engine.stop()
            
            # 停止Web服务器
            if hasattr(self, 'web_server') and self.web_server:
                try:
                    # 安全地停止Web服务器
                    self.web_server.should_exit = True
                    if hasattr(self.web_server, 'force_exit'):
                        self.web_server.force_exit = True
                    logger.info("✅ Web服务器停止信号已发送")
                except Exception as e:
                    logger.warning("Web服务器停止时出现警告", error=str(e))
            
            logger.info("✅ 套利交易系统已停止")
            
        except Exception as e:
            logger.error("系统停止失败", error=str(e))


async def main():
    """主函数"""
    system = ArbitrageSystem()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在停止系统...")
        asyncio.create_task(system.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await system.start()
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error("系统运行异常", error=str(e))
        sys.exit(1)
    finally:
        await system.stop()


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1) 