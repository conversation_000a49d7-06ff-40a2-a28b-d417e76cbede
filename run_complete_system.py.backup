#!/usr/bin/env python3
"""
完整套利交易系统启动脚本

集成所有模块：
1. 数据库管理
2. 套利引擎
3. Web监控界面
4. 实时交易执行
5. 风险管理
6. 数据持久化
"""

import asyncio
import signal
import sys
import os
import logging
import uvicorn
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.database.database import DatabaseManager
from src.database.trade_recorder import TradeRecorder
from src.arbitrage.engine import ArbitrageEngine
from src.web.app import create_web_app
from src.utils.config_loader import ConfigLoader
from src.utils.logger import setup_logger

# 配置日志
logger = setup_logger(__name__)


class ArbitrageTradingSystem:
    """完整套利交易系统"""

    def __init__(self, config_path: str = "config/settings.yaml", paper_trading: bool = None, enable_trading: bool = None):
        self.config_path = config_path
        self.config = None
        self.db_manager = None
        self.trade_recorder = None
        self.engine = None
        self.web_app = None
        self.web_server = None
        self.is_running = False

        # 交易模式设置
        self.paper_trading = paper_trading
        self.enable_trading = enable_trading

    async def initialize(self):
        """初始化系统"""
        logger.info("🚀 开始初始化Binance-Lighter套利交易系统...")

        try:
            # 1. 加载配置
            self.config = ConfigLoader.load_config(self.config_path)
            logger.info("✅ 配置加载完成")

            # 2. 初始化数据库
            db_path = self.config.get('database', {}).get('path', 'data/arbitrage.db')
            # 确保数据目录存在
            Path(db_path).parent.mkdir(parents=True, exist_ok=True)

            self.db_manager = DatabaseManager(db_path)
            await self.db_manager.initialize()
            logger.info(f"✅ 数据库初始化完成: {db_path}")

            # 3. 初始化交易记录器
            self.trade_recorder = TradeRecorder(self.db_manager)
            logger.info("✅ 交易记录器初始化完成")

            # 4. 初始化套利引擎
            self.engine = ArbitrageEngine(self.config)

            # 注入数据库组件
            self.engine.db_manager = self.db_manager
            self.engine.trade_recorder = self.trade_recorder

            # 从配置和命令行参数设置交易模式
            trading_config = self.config.get('trading', {})

            # 优先使用命令行参数，否则使用配置文件
            if self.paper_trading is not None:
                self.engine.is_paper_trading = self.paper_trading
            else:
                self.engine.is_paper_trading = trading_config.get('paper_trading', True)

            # 交易启用逻辑：
            # 1. 如果明确指定了 --enable-trading，则启用实际交易
            # 2. 如果指定了 --paper-trading，则启用纸上交易
            # 3. 否则使用配置文件设置
            if self.enable_trading is True:
                # 明确启用实际交易
                self.engine.is_trading_enabled = True
            elif self.paper_trading is True:
                # 启用纸上交易（交易功能开启，但是纸上模式）
                self.engine.is_trading_enabled = True
            else:
                # 使用配置文件设置
                self.engine.is_trading_enabled = trading_config.get('enabled', False)

            logger.info(f"✅ 套利引擎初始化完成 (纸上交易: {self.engine.is_paper_trading})")

            # 5. 创建Web应用
            self.web_app = create_web_app(self.engine)
            logger.info("✅ Web监控界面创建完成")

            # 6. 设置信号处理
            self._setup_signal_handlers()

            logger.info("🎉 系统初始化完成！")

        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            raise

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭...")
            # 避免在信号处理器中直接创建异步任务
            self.is_running = False
            # 设置停止标志，让主循环处理关闭
            if hasattr(self, '_shutdown_event'):
                self._shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 创建关闭事件
        self._shutdown_event = asyncio.Event()

    async def start_engine(self):
        """启动套利引擎"""
        logger.info("🔄 启动套利引擎...")

        try:
            # 初始化引擎（如果还没有初始化）
            if not self.engine.is_initialized:
                await self.engine.initialize()

            # 设置引擎为运行状态
            self.engine.is_running = True
            self.engine.stats['start_time'] = __import__('time').time()

            # 在后台启动引擎任务
            self.engine_task = asyncio.create_task(self._run_engine_tasks())

            logger.info("✅ 套利引擎启动成功")

            # 显示引擎状态
            status = self.engine.get_status()
            logger.info(f"📊 引擎状态: {status}")

        except Exception as e:
            logger.error(f"❌ 套利引擎启动失败: {e}")
            raise

    async def _run_engine_tasks(self):
        """运行引擎后台任务"""
        tasks = []
        try:
            # 启动定期检查任务
            periodic_task = asyncio.create_task(self.engine._periodic_check())
            tasks.append(periodic_task)

            # 启动系统监控任务
            monitoring_task = asyncio.create_task(self.engine._system_monitoring_task())
            tasks.append(monitoring_task)

            # 启动数据清理任务
            cleanup_task = asyncio.create_task(self.engine._data_cleanup_task())
            tasks.append(cleanup_task)

            # 等待停止信号或关闭事件
            try:
                # 同时等待任务完成和关闭信号
                done, pending = await asyncio.wait(
                    tasks + [asyncio.create_task(self._shutdown_event.wait())],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # 取消所有待处理的任务
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            except asyncio.CancelledError:
                logger.info("套利引擎任务被取消")

        except Exception as e:
            logger.error(f"引擎后台任务异常: {e}")
        finally:
            # 确保所有任务都被取消
            for task in tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

    def _find_available_port(self, start_port: int, max_attempts: int = 10) -> int:
        """查找可用端口"""
        import socket

        for i in range(max_attempts):
            port = start_port + i
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue

        raise Exception(f"无法找到可用端口，尝试了 {start_port} 到 {start_port + max_attempts - 1}")

    async def start_web_server(self):
        """启动Web服务器"""
        web_config = self.config.get('monitoring', {})
        host = web_config.get('web_host', '127.0.0.1')
        initial_port = web_config.get('web_port', 8000)

        # 查找可用端口
        try:
            port = self._find_available_port(initial_port)
            if port != initial_port:
                logger.info(f"🔄 端口 {initial_port} 被占用，使用端口 {port}")
        except Exception as e:
            logger.error(f"❌ 无法找到可用端口: {e}")
            raise

        logger.info(f"🌐 启动Web服务器: http://{host}:{port}")

        try:
            # 创建服务器配置
            config = uvicorn.Config(
                app=self.web_app,
                host=host,
                port=port,
                log_level="info",
                access_log=True
            )

            # 创建服务器实例
            self.web_server = uvicorn.Server(config)

            # 在后台任务中启动服务器
            self.web_server_task = asyncio.create_task(self.web_server.serve())

            # 等待服务器真正启动
            max_wait = 10  # 最多等待10秒
            wait_time = 0
            while not self.web_server.started and wait_time < max_wait:
                await asyncio.sleep(0.1)
                wait_time += 0.1

            if self.web_server.started:
                logger.info(f"✅ Web服务器启动成功: http://{host}:{port}")
                logger.info(f"📱 监控界面: http://{host}:{port}")
                # 更新配置中的端口信息
                self.actual_web_port = port
            else:
                logger.warning(f"⚠️ Web服务器启动超时，但任务已创建: http://{host}:{port}")

        except Exception as e:
            logger.error(f"❌ Web服务器启动失败: {e}")
            raise

    async def run_monitoring_tasks(self):
        """运行监控任务"""
        logger.info("📊 启动系统监控任务...")

        async def system_monitor():
            """系统监控任务"""
            while self.is_running:
                try:
                    # 记录系统状态
                    if hasattr(self.engine, 'record_system_status'):
                        await self.engine.record_system_status()

                    # 检查连接状态
                    if hasattr(self.engine, 'check_connections'):
                        await self.engine.check_connections()

                    # 等待60秒
                    await asyncio.sleep(60)

                except Exception as e:
                    logger.error(f"系统监控任务异常: {e}")
                    await asyncio.sleep(30)

        async def data_cleanup():
            """数据清理任务"""
            while self.is_running:
                try:
                    # 每天凌晨2点执行数据清理
                    now = datetime.now()
                    if now.hour == 2 and now.minute == 0:
                        logger.info("🧹 开始执行数据清理...")

                        # 清理30天前的数据
                        from datetime import timedelta, timezone
                        old_date = datetime.now(timezone.utc) - timedelta(days=30)
                        await self.db_manager.cleanup_old_data(old_date)

                        # 备份数据库
                        backup_path = await self.db_manager.backup_database()
                        logger.info(f"💾 数据库备份完成: {backup_path}")

                    # 等待1小时
                    await asyncio.sleep(3600)

                except Exception as e:
                    logger.error(f"数据清理任务异常: {e}")
                    await asyncio.sleep(1800)  # 出错时等待30分钟

        # 启动监控任务
        asyncio.create_task(system_monitor())
        asyncio.create_task(data_cleanup())

        logger.info("✅ 监控任务启动完成")

    async def start(self):
        """启动完整系统"""
        logger.info("🎯 启动完整套利交易系统...")

        try:
            # 初始化系统
            await self.initialize()

            # 启动套利引擎
            await self.start_engine()

            # 启动Web服务器
            await self.start_web_server()

            # 启动监控任务
            await self.run_monitoring_tasks()

            # 设置运行状态
            self.is_running = True

            logger.info("🎉 系统启动完成！")
            logger.info("=" * 60)
            logger.info("📊 系统状态:")
            logger.info(f"   🔧 套利引擎: {'运行中' if self.engine.is_running else '已停止'}")
            logger.info(f"   💰 交易模式: {'纸上交易' if self.engine.is_paper_trading else '实盘交易'}")
            logger.info(f"   🔄 交易状态: {'启用' if self.engine.is_trading_enabled else '禁用'}")

            # 使用实际的Web服务器端口
            web_host = self.config.get('monitoring', {}).get('web_host', '127.0.0.1')
            web_port = getattr(self, 'actual_web_port', self.config.get('monitoring', {}).get('web_port', 8000))
            logger.info(f"   🌐 Web界面: http://{web_host}:{web_port}")
            logger.info("=" * 60)

            # 显示实时状态
            await self.show_real_time_status()

        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            await self.shutdown()
            raise

    async def show_real_time_status(self):
        """显示实时状态"""
        logger.info("📈 开始显示实时状态 (按 Ctrl+C 停止)...")

        try:
            while self.is_running:
                try:
                    # 获取最近交易
                    recent_trades = await self.db_manager.get_recent_trades(5)

                    # 获取今日统计
                    from datetime import timezone
                    today = datetime.now(timezone.utc)
                    if self.trade_recorder:
                        daily_performance = await self.trade_recorder.calculate_daily_performance(today)

                        logger.info("=" * 50)
                        logger.info(f"⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                        logger.info(f"💰 今日盈利: {daily_performance.total_profit} USDT")
                        logger.info(f"📊 今日交易: {daily_performance.total_trades} 次")
                        logger.info(f"✅ 成功率: {daily_performance.success_rate:.1f}%")
                        logger.info(f"📈 最近交易数: {len(recent_trades)}")

                        # 显示连接状态
                        if hasattr(self.engine, 'connection_status'):
                            conn_status = self.engine.connection_status
                            logger.info(f"🔗 连接状态: Binance={conn_status.get('binance', False)}, Lighter={conn_status.get('lighter', False)}")

                    # 等待30秒或关闭信号
                    try:
                        await asyncio.wait_for(self._shutdown_event.wait(), timeout=30.0)
                        break  # 收到关闭信号
                    except asyncio.TimeoutError:
                        continue  # 超时，继续循环

                except Exception as e:
                    logger.error(f"状态显示循环异常: {e}")
                    await asyncio.sleep(5)

        except KeyboardInterrupt:
            logger.info("用户中断，开始关闭系统...")
            await self.shutdown()
        except Exception as e:
            logger.error(f"状态显示异常: {e}")

    async def shutdown(self):
        """优雅关闭系统"""
        if not self.is_running:
            return

        logger.info("🛑 开始关闭系统...")
        self.is_running = False

        try:
            # 1. 停止套利引擎任务
            if hasattr(self, 'engine_task') and self.engine_task:
                self.engine_task.cancel()
                try:
                    await self.engine_task
                except asyncio.CancelledError:
                    pass
                logger.info("✅ 套利引擎任务已停止")

            # 2. 停止套利引擎
            if self.engine:
                self.engine.is_running = False
                self.engine.is_trading_enabled = False
                # 取消所有活跃订单
                if hasattr(self.engine, '_cancel_all_active_orders'):
                    await self.engine._cancel_all_active_orders()
                # 停止客户端
                if hasattr(self.engine, 'binance_client') and self.engine.binance_client:
                    await self.engine.binance_client.close()
                if hasattr(self.engine, 'lighter_client') and self.engine.lighter_client:
                    await self.engine.lighter_client.close()
                logger.info("✅ 套利引擎已停止")

            # 3. 停止Web服务器
            if self.web_server:
                self.web_server.should_exit = True
                logger.info("✅ Web服务器已停止")

            # 4. 关闭数据库连接
            if self.db_manager:
                await self.db_manager.close()
                logger.info("✅ 数据库连接已关闭")

            logger.info("🎉 系统关闭完成")

        except Exception as e:
            logger.error(f"❌ 系统关闭异常: {e}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Binance-Lighter套利交易系统")
    parser.add_argument(
        "--config",
        default="config/settings.yaml",
        help="配置文件路径 (默认: config/settings.yaml)"
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="运行测试模式"
    )
    parser.add_argument(
        "--paper-trading",
        action="store_true",
        help="启用模拟交易模式（推荐用于测试）"
    )
    parser.add_argument(
        "--enable-trading",
        action="store_true",
        help="启用实际交易（谨慎使用）"
    )

    args = parser.parse_args()

    if args.test:
        # 运行测试
        logger.info("🧪 运行测试模式...")
        from test_complete_flow import ArbitrageFlowTester
        tester = ArbitrageFlowTester()
        await tester.run_all_tests()
    else:
        # 运行完整系统
        system = ArbitrageTradingSystem(
            config_path=args.config,
            paper_trading=args.paper_trading,
            enable_trading=args.enable_trading
        )

        # 显示交易模式信息
        if args.paper_trading:
            logger.info("📝 启用模拟交易模式")
        elif args.enable_trading:
            logger.info("💰 启用实际交易模式")
            logger.warning("⚠️ 注意：这是实际交易模式，请确保您了解风险！")
        else:
            logger.info("📝 默认使用模拟交易模式（安全模式）")

        await system.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)