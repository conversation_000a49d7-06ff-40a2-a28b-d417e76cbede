#!/usr/bin/env python3
"""
数据库迁移脚本 - 添加买入和卖出时间戳字段
"""

import sqlite3
import os
import shutil
from datetime import datetime

def migrate_database():
    """迁移数据库，添加新的时间戳字段"""
    
    db_path = "data/arbitrage.db"
    backup_path = f"data/arbitrage_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    
    print("🔄 开始数据库迁移...")
    
    # 检查数据库是否存在
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，无需迁移")
        return False
    
    try:
        # 备份原数据库
        print(f"📋 备份原数据库到: {backup_path}")
        shutil.copy2(db_path, backup_path)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已经有新字段
        cursor.execute("PRAGMA table_info(trades)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'binance_timestamp' in columns and 'lighter_timestamp' in columns:
            print("✅ 数据库已包含新字段，无需迁移")
            conn.close()
            return True
        
        print("🔧 添加新的时间戳字段...")
        
        # 添加新字段
        try:
            cursor.execute("ALTER TABLE trades ADD COLUMN binance_timestamp TEXT")
            print("   ✅ 添加 binance_timestamp 字段")
        except sqlite3.OperationalError as e:
            if "duplicate column name" not in str(e):
                raise
            print("   ℹ️ binance_timestamp 字段已存在")
        
        try:
            cursor.execute("ALTER TABLE trades ADD COLUMN lighter_timestamp TEXT")
            print("   ✅ 添加 lighter_timestamp 字段")
        except sqlite3.OperationalError as e:
            if "duplicate column name" not in str(e):
                raise
            print("   ℹ️ lighter_timestamp 字段已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证迁移
        cursor.execute("PRAGMA table_info(trades)")
        new_columns = [column[1] for column in cursor.fetchall()]
        
        if 'binance_timestamp' in new_columns and 'lighter_timestamp' in new_columns:
            print("✅ 数据库迁移成功！")
            
            # 显示表结构
            print("\n📋 更新后的表结构:")
            for i, column in enumerate(new_columns, 1):
                print(f"   {i:2d}. {column}")
            
            conn.close()
            return True
        else:
            print("❌ 数据库迁移失败")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        
        # 恢复备份
        if os.path.exists(backup_path):
            print("🔄 恢复备份数据库...")
            shutil.copy2(backup_path, db_path)
            print("✅ 数据库已恢复")
        
        return False

def create_test_data():
    """创建测试数据，包含新的时间戳字段"""
    
    db_path = "data/arbitrage.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已有测试数据
        cursor.execute("SELECT COUNT(*) FROM trades WHERE id LIKE 'test_%'")
        test_count = cursor.fetchone()[0]
        
        if test_count > 0:
            print(f"ℹ️ 已存在 {test_count} 条测试数据")
            conn.close()
            return True
        
        print("📝 创建测试数据...")
        
        # 创建测试交易记录
        now = datetime.now()
        test_trades = [
            {
                'id': 'test_001',
                'timestamp': now.isoformat(),
                'symbol': 'BTC/USDT',
                'trade_type': 'buy_arbitrage',
                'binance_order_id': 'binance_test_001',
                'binance_side': 'buy',
                'binance_price': 103500.00,
                'binance_quantity': 0.01,
                'binance_status': 'executed',
                'binance_fee': 0.50,
                'binance_timestamp': (now.replace(microsecond=123000)).isoformat(),
                'lighter_order_id': 'lighter_test_001',
                'lighter_side': 'sell',
                'lighter_price': 103520.00,
                'lighter_quantity': 0.01,
                'lighter_status': 'executed',
                'lighter_fee': 0.30,
                'lighter_timestamp': (now.replace(microsecond=456000)).isoformat(),
                'expected_profit': 1.50,
                'actual_profit': 1.20,
                'execution_time_ms': 150,
                'status': 'executed'
            },
            {
                'id': 'test_002',
                'timestamp': (now.replace(second=now.second-30)).isoformat(),
                'symbol': 'BTC/USDT',
                'trade_type': 'sell_arbitrage',
                'binance_order_id': 'binance_test_002',
                'binance_side': 'sell',
                'binance_price': 103480.00,
                'binance_quantity': 0.01,
                'binance_status': 'executed',
                'binance_fee': 0.50,
                'binance_timestamp': (now.replace(second=now.second-30, microsecond=789000)).isoformat(),
                'lighter_order_id': 'lighter_test_002',
                'lighter_side': 'buy',
                'lighter_price': 103460.00,
                'lighter_quantity': 0.01,
                'lighter_status': 'executed',
                'lighter_fee': 0.30,
                'lighter_timestamp': (now.replace(second=now.second-30, microsecond=234000)).isoformat(),
                'expected_profit': 1.80,
                'actual_profit': -0.60,
                'execution_time_ms': 200,
                'status': 'executed'
            }
        ]
        
        # 插入测试数据
        for trade in test_trades:
            cursor.execute("""
                INSERT OR REPLACE INTO trades (
                    id, timestamp, symbol, trade_type,
                    binance_order_id, binance_side, binance_price, binance_quantity, binance_status, binance_fee, binance_timestamp,
                    lighter_order_id, lighter_side, lighter_price, lighter_quantity, lighter_status, lighter_fee, lighter_timestamp,
                    expected_profit, actual_profit, execution_time_ms, status
                ) VALUES (
                    :id, :timestamp, :symbol, :trade_type,
                    :binance_order_id, :binance_side, :binance_price, :binance_quantity, :binance_status, :binance_fee, :binance_timestamp,
                    :lighter_order_id, :lighter_side, :lighter_price, :lighter_quantity, :lighter_status, :lighter_fee, :lighter_timestamp,
                    :expected_profit, :actual_profit, :execution_time_ms, :status
                )
            """, trade)
        
        conn.commit()
        conn.close()
        
        print(f"✅ 创建了 {len(test_trades)} 条测试数据")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 数据库迁移工具")
    print("=" * 50)
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 执行迁移
    if migrate_database():
        print("\n📝 创建测试数据...")
        create_test_data()
        
        print("\n🎉 迁移完成！")
        print("\n📋 新功能:")
        print("   ✅ 分别记录Binance和Lighter的订单执行时间")
        print("   ✅ 前端显示买入和卖出的具体时间")
        print("   ✅ 时间精确到毫秒")
        print("   ✅ 本地时间格式显示")
        
        print("\n🌐 测试方法:")
        print("   1. 启动系统: python3 run_complete_system.py --paper-trading")
        print("   2. 访问主页: http://localhost:8000")
        print("   3. 访问测试页: http://localhost:8000/test-time")
        
        return True
    else:
        print("\n❌ 迁移失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
