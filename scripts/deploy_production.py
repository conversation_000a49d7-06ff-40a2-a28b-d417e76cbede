#!/usr/bin/env python3
"""
生产环境部署脚本

自动化部署和配置生产环境
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
from typing import Dict, List, Optional
import yaml
import structlog

# 设置基础日志
logging_config = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "plain": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "plain",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default"],
            "level": "INFO",
            "propagate": True,
        }
    }
}

import logging.config
logging.config.dictConfig(logging_config)

structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class ProductionDeployer:
    """生产环境部署器"""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_dir = self.project_root / "config"
        self.data_dir = self.project_root / "data"
        self.logs_dir = self.project_root / "logs"
        
        # 部署状态
        self.deployment_steps = [
            "环境检查",
            "依赖安装",
            "配置验证",
            "数据库初始化",
            "目录创建",
            "权限设置",
            "服务配置",
            "健康检查",
            "启动服务"
        ]
        
        self.completed_steps = []
        self.failed_steps = []

    def deploy(self, config_file: str = "config/production.yaml", 
               skip_checks: bool = False, 
               dry_run: bool = False):
        """执行部署"""
        try:
            logger.info("🚀 开始生产环境部署", 
                       config_file=config_file, 
                       dry_run=dry_run)
            
            # 1. 环境检查
            if not skip_checks:
                self._check_environment()
            
            # 2. 依赖安装
            self._install_dependencies(dry_run)
            
            # 3. 配置验证
            self._validate_configuration(config_file)
            
            # 4. 数据库初始化
            self._initialize_database(dry_run)
            
            # 5. 目录创建
            self._create_directories(dry_run)
            
            # 6. 权限设置
            self._set_permissions(dry_run)
            
            # 7. 服务配置
            self._configure_services(dry_run)
            
            # 8. 健康检查
            self._health_check()
            
            # 9. 启动服务
            if not dry_run:
                self._start_services(config_file)
            
            logger.info("✅ 生产环境部署完成")
            self._print_deployment_summary()
            
        except Exception as e:
            logger.error("❌ 部署失败", error=str(e))
            self._print_deployment_summary()
            raise

    def _check_environment(self):
        """检查环境"""
        logger.info("🔍 检查环境...")
        
        try:
            # 检查Python版本
            python_version = sys.version_info
            if python_version < (3, 8):
                raise RuntimeError(f"需要Python 3.8+，当前版本: {python_version}")
            
            # 检查必需的环境变量
            required_env_vars = [
                "BINANCE_API_KEY",
                "BINANCE_SECRET_KEY", 
                "LIGHTER_PRIVATE_KEY",
                "WEB_AUTH_TOKEN",
                "ENCRYPTION_KEY"
            ]
            
            missing_vars = []
            for var in required_env_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
            
            if missing_vars:
                logger.warning("缺少环境变量", missing_vars=missing_vars)
                print("\n⚠️  缺少以下环境变量:")
                for var in missing_vars:
                    print(f"   {var}")
                print("\n请设置这些环境变量后重新运行部署脚本。")
                print("示例:")
                print("export BINANCE_API_KEY='your_api_key'")
                print("export BINANCE_SECRET_KEY='your_secret_key'")
                print("export LIGHTER_PRIVATE_KEY='your_private_key'")
                print("export WEB_AUTH_TOKEN='your_auth_token'")
                print("export ENCRYPTION_KEY='your_encryption_key'")
                
                response = input("\n是否继续部署? (y/N): ")
                if response.lower() != 'y':
                    raise RuntimeError("部署被用户取消")
            
            # 检查磁盘空间
            disk_usage = shutil.disk_usage(self.project_root)
            free_gb = disk_usage.free / (1024**3)
            if free_gb < 1.0:
                raise RuntimeError(f"磁盘空间不足，需要至少1GB，当前可用: {free_gb:.2f}GB")
            
            # 检查网络连接
            self._check_network_connectivity()
            
            self.completed_steps.append("环境检查")
            logger.info("✅ 环境检查通过")
            
        except Exception as e:
            self.failed_steps.append("环境检查")
            logger.error("❌ 环境检查失败", error=str(e))
            raise

    def _check_network_connectivity(self):
        """检查网络连接"""
        import socket
        
        test_hosts = [
            ("api.binance.com", 443),
            ("api.lighter.xyz", 443),
            ("*******", 53)  # Google DNS
        ]
        
        for host, port in test_hosts:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result != 0:
                    logger.warning("网络连接测试失败", host=host, port=port)
                else:
                    logger.debug("网络连接测试成功", host=host, port=port)
                    
            except Exception as e:
                logger.warning("网络连接测试异常", host=host, error=str(e))

    def _install_dependencies(self, dry_run: bool):
        """安装依赖"""
        logger.info("📦 安装依赖...")
        
        try:
            requirements_file = self.project_root / "requirements.txt"
            
            if not requirements_file.exists():
                raise FileNotFoundError("requirements.txt文件不存在")
            
            if not dry_run:
                # 升级pip
                subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                             check=True, capture_output=True)
                
                # 安装依赖
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                             check=True, capture_output=True)
                
                # 安装Lighter SDK
                try:
                    subprocess.run([sys.executable, "-m", "pip", "install", 
                                  "git+https://github.com/elliottech/lighter-python.git"], 
                                 check=True, capture_output=True)
                except subprocess.CalledProcessError:
                    logger.warning("Lighter SDK安装失败，将使用模拟模式")
            
            self.completed_steps.append("依赖安装")
            logger.info("✅ 依赖安装完成")
            
        except Exception as e:
            self.failed_steps.append("依赖安装")
            logger.error("❌ 依赖安装失败", error=str(e))
            raise

    def _validate_configuration(self, config_file: str):
        """验证配置"""
        logger.info("⚙️ 验证配置...")
        
        try:
            config_path = Path(config_file)
            
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_file}")
            
            # 加载配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 验证必需的配置段
            required_sections = ['trading', 'risk_management', 'exchanges', 'database']
            for section in required_sections:
                if section not in config:
                    raise ValueError(f"配置文件缺少必需段: {section}")
            
            # 验证交易所配置
            exchanges = config['exchanges']
            if 'binance' not in exchanges or 'lighter' not in exchanges:
                raise ValueError("配置文件缺少交易所配置")
            
            # 验证关键参数
            trading_config = config['trading']
            if trading_config.get('paper_trading', True):
                logger.warning("⚠️ 配置为模拟交易模式")
            
            self.completed_steps.append("配置验证")
            logger.info("✅ 配置验证通过")
            
        except Exception as e:
            self.failed_steps.append("配置验证")
            logger.error("❌ 配置验证失败", error=str(e))
            raise

    def _initialize_database(self, dry_run: bool):
        """初始化数据库"""
        logger.info("🗄️ 初始化数据库...")
        
        try:
            if not dry_run:
                # 确保数据目录存在
                self.data_dir.mkdir(exist_ok=True)
                
                # 创建备份目录
                backup_dir = self.data_dir / "backups"
                backup_dir.mkdir(exist_ok=True)
                
                # 初始化数据库（通过导入模块触发初始化）
                sys.path.insert(0, str(self.project_root))
                from src.database.database import DatabaseManager
                
                db_manager = DatabaseManager("data/production.db")
                # 数据库会在第一次使用时自动初始化
            
            self.completed_steps.append("数据库初始化")
            logger.info("✅ 数据库初始化完成")
            
        except Exception as e:
            self.failed_steps.append("数据库初始化")
            logger.error("❌ 数据库初始化失败", error=str(e))
            raise

    def _create_directories(self, dry_run: bool):
        """创建目录"""
        logger.info("📁 创建目录...")
        
        try:
            directories = [
                self.data_dir,
                self.data_dir / "backups",
                self.logs_dir,
                self.project_root / "tmp"
            ]
            
            for directory in directories:
                if not dry_run:
                    directory.mkdir(exist_ok=True)
                logger.debug("创建目录", path=str(directory))
            
            self.completed_steps.append("目录创建")
            logger.info("✅ 目录创建完成")
            
        except Exception as e:
            self.failed_steps.append("目录创建")
            logger.error("❌ 目录创建失败", error=str(e))
            raise

    def _set_permissions(self, dry_run: bool):
        """设置权限"""
        logger.info("🔐 设置权限...")
        
        try:
            if not dry_run and os.name != 'nt':  # 非Windows系统
                # 设置数据目录权限
                os.chmod(self.data_dir, 0o755)
                
                # 设置日志目录权限
                os.chmod(self.logs_dir, 0o755)
                
                # 设置配置文件权限（只有所有者可读写）
                for config_file in self.config_dir.glob("*.yaml"):
                    if config_file.name != "production.yaml":  # 跳过模板文件
                        os.chmod(config_file, 0o600)
            
            self.completed_steps.append("权限设置")
            logger.info("✅ 权限设置完成")
            
        except Exception as e:
            self.failed_steps.append("权限设置")
            logger.error("❌ 权限设置失败", error=str(e))
            raise

    def _configure_services(self, dry_run: bool):
        """配置服务"""
        logger.info("⚙️ 配置服务...")
        
        try:
            # 创建systemd服务文件（Linux）
            if not dry_run and os.name != 'nt':
                self._create_systemd_service()
            
            # 创建启动脚本
            self._create_startup_script(dry_run)
            
            self.completed_steps.append("服务配置")
            logger.info("✅ 服务配置完成")
            
        except Exception as e:
            self.failed_steps.append("服务配置")
            logger.error("❌ 服务配置失败", error=str(e))
            raise

    def _create_systemd_service(self):
        """创建systemd服务文件"""
        service_content = f"""[Unit]
Description=Arbitrage Trading System
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'root')}
WorkingDirectory={self.project_root}
Environment=PATH={os.environ.get('PATH')}
ExecStart={sys.executable} run_complete_system.py --config config/production.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_file = Path("/etc/systemd/system/arbitrage-trading.service")
        try:
            with open(service_file, 'w') as f:
                f.write(service_content)
            logger.info("systemd服务文件已创建", path=str(service_file))
        except PermissionError:
            logger.warning("无法创建systemd服务文件，需要root权限")

    def _create_startup_script(self, dry_run: bool):
        """创建启动脚本"""
        script_content = f"""#!/bin/bash
# 套利交易系统启动脚本

cd {self.project_root}

# 设置环境变量（如果未设置）
export PYTHONPATH={self.project_root}

# 启动系统
{sys.executable} run_complete_system.py --config config/production.yaml "$@"
"""
        
        script_file = self.project_root / "start_production.sh"
        
        if not dry_run:
            with open(script_file, 'w') as f:
                f.write(script_content)
            
            # 设置执行权限
            if os.name != 'nt':
                os.chmod(script_file, 0o755)
        
        logger.info("启动脚本已创建", path=str(script_file))

    def _health_check(self):
        """健康检查"""
        logger.info("🏥 执行健康检查...")
        
        try:
            # 检查Python模块导入
            sys.path.insert(0, str(self.project_root))
            
            modules_to_check = [
                "src.utils.config_loader",
                "src.exchanges.binance_client",
                "src.exchanges.lighter_client",
                "src.arbitrage.engine",
                "src.monitoring.system_monitor",
                "src.trading.order_manager",
                "src.optimization.performance_optimizer"
            ]
            
            for module_name in modules_to_check:
                try:
                    __import__(module_name)
                    logger.debug("模块导入成功", module=module_name)
                except ImportError as e:
                    logger.error("模块导入失败", module=module_name, error=str(e))
                    raise
            
            self.completed_steps.append("健康检查")
            logger.info("✅ 健康检查通过")
            
        except Exception as e:
            self.failed_steps.append("健康检查")
            logger.error("❌ 健康检查失败", error=str(e))
            raise

    def _start_services(self, config_file: str):
        """启动服务"""
        logger.info("🚀 启动服务...")
        
        try:
            print("\n" + "="*60)
            print("🎉 部署完成！")
            print("="*60)
            print(f"\n启动命令:")
            print(f"  python run_complete_system.py --config {config_file}")
            print(f"\n或使用启动脚本:")
            print(f"  ./start_production.sh")
            
            if os.name != 'nt':
                print(f"\n使用systemd管理服务:")
                print(f"  sudo systemctl enable arbitrage-trading")
                print(f"  sudo systemctl start arbitrage-trading")
                print(f"  sudo systemctl status arbitrage-trading")
            
            print(f"\nWeb监控界面:")
            print(f"  http://localhost:8000")
            print("\n" + "="*60)
            
            response = input("\n是否立即启动系统? (y/N): ")
            if response.lower() == 'y':
                os.system(f"{sys.executable} run_complete_system.py --config {config_file}")
            
            self.completed_steps.append("启动服务")
            logger.info("✅ 服务启动完成")
            
        except Exception as e:
            self.failed_steps.append("启动服务")
            logger.error("❌ 服务启动失败", error=str(e))
            raise

    def _print_deployment_summary(self):
        """打印部署摘要"""
        print("\n" + "="*60)
        print("📊 部署摘要")
        print("="*60)
        
        print(f"\n✅ 完成的步骤 ({len(self.completed_steps)}):")
        for step in self.completed_steps:
            print(f"  ✓ {step}")
        
        if self.failed_steps:
            print(f"\n❌ 失败的步骤 ({len(self.failed_steps)}):")
            for step in self.failed_steps:
                print(f"  ✗ {step}")
        
        total_steps = len(self.deployment_steps)
        completed_count = len(self.completed_steps)
        success_rate = (completed_count / total_steps) * 100
        
        print(f"\n📈 成功率: {success_rate:.1f}% ({completed_count}/{total_steps})")
        
        if success_rate == 100:
            print("\n🎉 部署完全成功！")
        elif success_rate >= 80:
            print("\n⚠️ 部署基本成功，但有部分问题需要解决。")
        else:
            print("\n❌ 部署失败，请检查错误信息并重新部署。")
        
        print("="*60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生产环境部署脚本")
    parser.add_argument("--config", default="config/production.yaml", 
                       help="配置文件路径")
    parser.add_argument("--skip-checks", action="store_true", 
                       help="跳过环境检查")
    parser.add_argument("--dry-run", action="store_true", 
                       help="干运行模式（不执行实际操作）")
    
    args = parser.parse_args()
    
    deployer = ProductionDeployer()
    
    try:
        deployer.deploy(
            config_file=args.config,
            skip_checks=args.skip_checks,
            dry_run=args.dry_run
        )
    except KeyboardInterrupt:
        logger.info("部署被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error("部署失败", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
