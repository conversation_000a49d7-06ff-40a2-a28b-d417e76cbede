# 🔧 工具脚本目录

这个目录包含了系统部署、维护和管理的工具脚本。

## 📋 脚本列表

### 🚀 部署脚本
- **`deploy_production.py`** - 生产环境部署脚本
  - 自动化部署流程
  - 环境配置检查
  - 服务启动和监控
  - 安全配置验证

### 🗄️ 数据库管理
- **`database_migration.py`** - 数据库迁移脚本
  - 数据库结构升级
  - 数据迁移和备份
  - 版本兼容性处理
  - 安全的数据库更新

## 🚀 使用方法

### 部署到生产环境
```bash
# 部署到生产环境
python scripts/deploy_production.py

# 检查部署状态
python scripts/deploy_production.py --status

# 回滚部署
python scripts/deploy_production.py --rollback
```

### 数据库管理
```bash
# 执行数据库迁移
python scripts/database_migration.py

# 备份数据库
python scripts/database_migration.py --backup

# 恢复数据库
python scripts/database_migration.py --restore backup_file.db
```

## 📊 脚本功能详情

### deploy_production.py
- **环境检查**: 验证生产环境配置
- **依赖安装**: 自动安装所需依赖
- **配置部署**: 部署配置文件
- **服务启动**: 启动系统服务
- **健康检查**: 验证部署成功
- **监控设置**: 配置监控和告警

### database_migration.py
- **结构升级**: 安全的数据库结构更新
- **数据迁移**: 保持数据完整性的迁移
- **版本管理**: 数据库版本控制
- **备份恢复**: 自动备份和恢复功能
- **兼容性检查**: 确保向后兼容

## ⚠️ 安全注意事项

### 生产部署
1. **备份数据**: 部署前务必备份所有数据
2. **测试环境**: 先在测试环境验证
3. **API密钥**: 确保生产环境API密钥安全
4. **网络安全**: 配置防火墙和访问控制

### 数据库操作
1. **数据备份**: 操作前自动创建备份
2. **事务安全**: 使用事务确保数据一致性
3. **权限控制**: 限制数据库访问权限
4. **监控日志**: 记录所有数据库操作

## 🔧 配置要求

### 系统要求
- Python 3.8+
- 足够的磁盘空间
- 网络连接
- 适当的系统权限

### 环境变量
```bash
# 生产环境配置
export ENVIRONMENT=production
export LOG_LEVEL=INFO
export DATABASE_PATH=/path/to/production.db

# API配置
export BINANCE_API_KEY=your_api_key
export BINANCE_SECRET_KEY=your_secret_key
export LIGHTER_API_KEY=your_lighter_key
```

## 📈 监控和日志

### 部署监控
- 系统资源使用情况
- 服务运行状态
- API连接状态
- 交易执行状态

### 日志管理
- 部署操作日志
- 数据库操作日志
- 错误和异常日志
- 性能监控日志

## 🔄 维护流程

### 定期维护
1. **数据库优化**: 定期清理和优化数据库
2. **日志轮转**: 管理日志文件大小
3. **性能监控**: 监控系统性能指标
4. **安全更新**: 定期更新依赖和安全补丁

### 故障恢复
1. **快速诊断**: 使用诊断脚本快速定位问题
2. **自动恢复**: 自动重启失败的服务
3. **数据恢复**: 从备份恢复数据
4. **通知机制**: 及时通知管理员

## 📞 获取帮助

如果遇到脚本问题：

1. 查看脚本的详细注释
2. 检查系统日志文件
3. 验证环境配置
4. 参考主文档的故障排除部分

## 🔗 相关文档

- [安全配置指南](../docs/SECURITY_GUIDE.md) - API密钥和安全配置
- [系统状态文档](../docs/SYSTEM_STATUS.md) - 系统状态和性能指标
- [进程管理指南](../docs/PROCESS_MANAGEMENT.md) - 进程管理和监控

---

**最后更新**: 2025-05-31
**脚本版本**: v2.2.0
