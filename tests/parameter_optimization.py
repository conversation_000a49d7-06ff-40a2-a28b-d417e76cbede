#!/usr/bin/env python3
"""
交易参数优化脚本
基于实时监控数据动态调整交易阈值
"""

import asyncio
import json
import yaml
import time
from datetime import datetime, timedelta
import requests
import structlog

logger = structlog.get_logger(__name__)

class TradingParameterOptimizer:
    def __init__(self):
        self.config_path = "config/settings.yaml"
        self.api_base = "http://localhost:8000"
        self.optimization_history = []

    def load_current_config(self):
        """加载当前配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return None

    def save_config(self, config):
        """保存配置"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            logger.info("✅ 配置保存成功")
            return True
        except Exception as e:
            logger.error(f"❌ 配置保存失败: {e}")
            return False

    def get_system_status(self):
        """获取系统状态"""
        try:
            response = requests.get(f"{self.api_base}/api/status", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return None

    def get_price_data(self):
        """获取价格数据"""
        try:
            response = requests.get(f"{self.api_base}/api/prices", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
            return None

    def analyze_spread_history(self, duration_minutes=10):
        """分析价差历史数据"""
        logger.info(f"📊 分析过去{duration_minutes}分钟的价差数据...")

        spreads = []
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)

        while time.time() < end_time:
            price_data = self.get_price_data()
            if price_data and not price_data.get('error'):
                try:
                    # 修正数据结构访问
                    binance_prices = price_data.get('binance_prices', {})
                    lighter_prices = price_data.get('lighter_prices', {})

                    binance_price = binance_prices.get('last', 0)
                    lighter_price = lighter_prices.get('last', 0)

                    if binance_price > 0 and lighter_price > 0:
                        spread = abs(binance_price - lighter_price)
                        spread_pct = (spread / min(binance_price, lighter_price)) * 100

                        spreads.append({
                            'timestamp': datetime.now().isoformat(),
                            'binance_price': binance_price,
                            'lighter_price': lighter_price,
                            'spread': spread,
                            'spread_pct': spread_pct
                        })

                        logger.debug(f"价差: {spread:.2f} USDT ({spread_pct:.3f}%)")
                    else:
                        logger.debug(f"价格数据无效: Binance={binance_price}, Lighter={lighter_price}")

                except (ValueError, KeyError) as e:
                    logger.warning(f"价格数据解析失败: {e}")
                    logger.debug(f"原始数据: {price_data}")
            else:
                logger.warning(f"API返回错误或无数据: {price_data}")

            time.sleep(5)  # 每5秒检查一次

        return spreads

    def calculate_optimal_thresholds(self, spreads):
        """计算最优阈值"""
        if not spreads:
            logger.warning("没有价差数据，无法计算最优阈值")
            return None

        spread_pcts = [s['spread_pct'] for s in spreads]

        # 统计分析
        avg_spread = sum(spread_pcts) / len(spread_pcts)
        max_spread = max(spread_pcts)
        min_spread = min(spread_pcts)

        # 计算百分位数
        sorted_spreads = sorted(spread_pcts)
        p25 = sorted_spreads[int(len(sorted_spreads) * 0.25)]
        p50 = sorted_spreads[int(len(sorted_spreads) * 0.50)]
        p75 = sorted_spreads[int(len(sorted_spreads) * 0.75)]
        p90 = sorted_spreads[int(len(sorted_spreads) * 0.90)]

        logger.info(f"📈 价差统计:")
        logger.info(f"   平均价差: {avg_spread:.3f}%")
        logger.info(f"   最大价差: {max_spread:.3f}%")
        logger.info(f"   最小价差: {min_spread:.3f}%")
        logger.info(f"   25%分位: {p25:.3f}%")
        logger.info(f"   50%分位: {p50:.3f}%")
        logger.info(f"   75%分位: {p75:.3f}%")
        logger.info(f"   90%分位: {p90:.3f}%")

        # 计算建议阈值
        # 最小阈值：略高于75%分位数，确保有足够的套利机会
        min_threshold = max(p75 * 1.1, 0.05)  # 至少0.05%

        # 最大阈值：基于90%分位数，避免过度风险
        max_threshold = min(p90 * 1.5, 2.0)  # 最多2.0%

        # 目标阈值：在最小和最大之间的平衡点
        target_threshold = (min_threshold + max_threshold) / 2

        return {
            'statistics': {
                'avg_spread': avg_spread,
                'max_spread': max_spread,
                'min_spread': min_spread,
                'p25': p25,
                'p50': p50,
                'p75': p75,
                'p90': p90
            },
            'recommended_thresholds': {
                'min_spread_pct': round(min_threshold, 3),
                'target_spread_pct': round(target_threshold, 3),
                'max_spread_pct': round(max_threshold, 3)
            }
        }

    def optimize_parameters(self, analysis_duration=10):
        """优化交易参数"""
        logger.info("🎯 开始交易参数优化...")

        # 1. 加载当前配置
        current_config = self.load_current_config()
        if not current_config:
            logger.error("❌ 无法加载当前配置")
            return False

        # 2. 分析价差历史
        spreads = self.analyze_spread_history(analysis_duration)
        if not spreads:
            logger.error("❌ 无法获取价差数据")
            return False

        # 3. 计算最优阈值
        optimization_result = self.calculate_optimal_thresholds(spreads)
        if not optimization_result:
            logger.error("❌ 无法计算最优阈值")
            return False

        # 4. 更新配置
        recommended = optimization_result['recommended_thresholds']

        # 备份当前配置
        backup_config = current_config.copy()

        # 更新阈值
        if 'arbitrage' not in current_config:
            current_config['arbitrage'] = {}
        if 'thresholds' not in current_config['arbitrage']:
            current_config['arbitrage']['thresholds'] = {}

        current_config['arbitrage']['thresholds'].update({
            'min_spread_pct': recommended['min_spread_pct'],
            'max_spread_pct': recommended['max_spread_pct'],
            'target_spread_pct': recommended['target_spread_pct']
        })

        # 5. 保存优化记录
        optimization_record = {
            'timestamp': datetime.now().isoformat(),
            'analysis_duration': analysis_duration,
            'data_points': len(spreads),
            'statistics': optimization_result['statistics'],
            'old_thresholds': backup_config.get('arbitrage', {}).get('thresholds', {}),
            'new_thresholds': recommended,
            'improvement_expected': True
        }

        self.optimization_history.append(optimization_record)

        # 6. 保存配置
        if self.save_config(current_config):
            logger.info("✅ 参数优化完成")
            logger.info(f"🎯 新的交易阈值:")
            logger.info(f"   最小价差: {recommended['min_spread_pct']}%")
            logger.info(f"   目标价差: {recommended['target_spread_pct']}%")
            logger.info(f"   最大价差: {recommended['max_spread_pct']}%")

            # 保存优化历史
            self.save_optimization_history()
            return True
        else:
            logger.error("❌ 配置保存失败")
            return False

    def save_optimization_history(self):
        """保存优化历史"""
        try:
            with open("optimization_history.json", "w", encoding="utf-8") as f:
                json.dump(self.optimization_history, f, indent=2, ensure_ascii=False)
            logger.info("📋 优化历史已保存")
        except Exception as e:
            logger.error(f"保存优化历史失败: {e}")

    def generate_optimization_report(self):
        """生成优化报告"""
        if not self.optimization_history:
            logger.warning("没有优化历史数据")
            return

        latest = self.optimization_history[-1]

        report = f"""
# 🎯 交易参数优化报告

## 📊 优化执行信息
- **优化时间**: {latest['timestamp']}
- **分析时长**: {latest['analysis_duration']} 分钟
- **数据点数**: {latest['data_points']} 个

## 📈 市场价差分析
- **平均价差**: {latest['statistics']['avg_spread']:.3f}%
- **最大价差**: {latest['statistics']['max_spread']:.3f}%
- **最小价差**: {latest['statistics']['min_spread']:.3f}%
- **中位数价差**: {latest['statistics']['p50']:.3f}%

## 🎯 阈值调整结果
### 调整前
- 最小价差阈值: {latest['old_thresholds'].get('min_spread_pct', 'N/A')}%

### 调整后
- **最小价差阈值**: {latest['new_thresholds']['min_spread_pct']}%
- **目标价差阈值**: {latest['new_thresholds']['target_spread_pct']}%
- **最大价差阈值**: {latest['new_thresholds']['max_spread_pct']}%

## 💡 优化建议
基于当前市场条件，建议的参数调整能够：
1. 提高套利机会捕获率
2. 降低交易风险
3. 优化收益风险比

## ⚠️ 注意事项
- 请在小额资金下测试新参数
- 密切监控系统表现
- 根据市场变化及时调整

---
*报告生成时间: {datetime.now().isoformat()}*
"""

        with open("optimization_report.md", "w", encoding="utf-8") as f:
            f.write(report)

        logger.info("📋 优化报告已生成: optimization_report.md")

async def main():
    """主函数"""
    logger.info("🚀 启动交易参数优化器...")

    optimizer = TradingParameterOptimizer()

    try:
        # 执行参数优化
        success = optimizer.optimize_parameters(analysis_duration=5)  # 5分钟分析

        if success:
            # 生成优化报告
            optimizer.generate_optimization_report()
            logger.info("🎉 参数优化完成！")
        else:
            logger.error("❌ 参数优化失败")

    except Exception as e:
        logger.error(f"❌ 优化过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
