#!/usr/bin/env python3
"""
完整集成测试脚本

测试修复后的Lighter客户端集成、监控系统、扩展功能和性能优化
"""

import asyncio
import time
import sys
import signal
from pathlib import Path
from typing import Dict, Any
import structlog

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config_loader import ConfigLoader
from src.utils.logger import setup_logging
from src.exchanges.lighter_client import LighterClient
from src.exchanges.binance_client import BinanceClient
from src.monitoring.system_monitor import SystemMonitor
from src.trading.order_manager import OrderManager, OrderRequest, OrderType, OrderSide
from src.optimization.performance_optimizer import PerformanceOptimizer

# 设置日志
setup_logging()
logger = structlog.get_logger(__name__)


class IntegrationTester:
    """集成测试器"""

    def __init__(self):
        self.config = None
        self.lighter_client = None
        self.binance_client = None
        self.system_monitor = None
        self.order_manager = None
        self.performance_optimizer = None
        self.is_running = False
        
        # 测试统计
        self.test_results = {
            'lighter_client': {'status': 'pending', 'details': {}},
            'binance_client': {'status': 'pending', 'details': {}},
            'system_monitor': {'status': 'pending', 'details': {}},
            'order_manager': {'status': 'pending', 'details': {}},
            'performance_optimizer': {'status': 'pending', 'details': {}},
            'integration': {'status': 'pending', 'details': {}}
        }

    async def run_complete_test(self):
        """运行完整集成测试"""
        try:
            logger.info("🚀 开始完整集成测试")
            
            # 1. 加载配置
            await self._test_configuration()
            
            # 2. 测试Lighter客户端
            await self._test_lighter_client()
            
            # 3. 测试Binance客户端
            await self._test_binance_client()
            
            # 4. 测试系统监控
            await self._test_system_monitor()
            
            # 5. 测试订单管理器
            await self._test_order_manager()
            
            # 6. 测试性能优化器
            await self._test_performance_optimizer()
            
            # 7. 测试集成功能
            await self._test_integration()
            
            # 8. 生成测试报告
            self._generate_test_report()
            
            logger.info("✅ 完整集成测试完成")
            
        except Exception as e:
            logger.error("❌ 集成测试失败", error=str(e))
            raise

    async def _test_configuration(self):
        """测试配置加载"""
        try:
            logger.info("📋 测试配置加载...")
            
            # 加载配置
            self.config = ConfigLoader.load_config("config/settings.yaml")
            
            # 验证配置
            is_valid = ConfigLoader.validate_config(self.config)
            
            if is_valid:
                logger.info("✅ 配置加载和验证成功")
            else:
                raise ValueError("配置验证失败")
                
        except Exception as e:
            logger.error("❌ 配置测试失败", error=str(e))
            raise

    async def _test_lighter_client(self):
        """测试Lighter客户端"""
        try:
            logger.info("🔌 测试Lighter客户端...")
            
            # 创建客户端
            self.lighter_client = LighterClient(
                symbol="BTC/USDT",
                is_paper_trading=True
            )
            
            # 初始化
            success = await self.lighter_client.initialize()
            
            if not success:
                raise RuntimeError("Lighter客户端初始化失败")
            
            # 等待数据更新
            logger.info("⏳ 等待Lighter数据更新...")
            await asyncio.sleep(10)
            
            # 检查数据
            orderbook = self.lighter_client.get_orderbook("BTC/USDT")
            current_price = self.lighter_client.get_current_price("BTC/USDT")
            status = self.lighter_client.get_status()
            
            self.test_results['lighter_client'] = {
                'status': 'success',
                'details': {
                    'initialized': success,
                    'has_orderbook': bool(orderbook and orderbook.get('bids') and orderbook.get('asks')),
                    'has_price': current_price is not None,
                    'connection_status': status.get('connection_status'),
                    'last_update': status.get('last_update')
                }
            }
            
            logger.info("✅ Lighter客户端测试成功", **self.test_results['lighter_client']['details'])
            
        except Exception as e:
            logger.error("❌ Lighter客户端测试失败", error=str(e))
            self.test_results['lighter_client'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }

    async def _test_binance_client(self):
        """测试Binance客户端"""
        try:
            logger.info("🏦 测试Binance客户端...")
            
            # 创建客户端（使用测试网）
            binance_config = self.config.get('exchanges', {}).get('binance', {})
            self.binance_client = BinanceClient(
                api_key=binance_config.get('api_key', 'test'),
                secret=binance_config.get('secret', 'test'),
                sandbox=True,
                testnet=True,
                is_paper_trading=True
            )
            
            # 初始化
            await self.binance_client.initialize()
            
            # 测试功能
            ticker = await self.binance_client.get_ticker("BTCUSDT")
            orderbook = await self.binance_client.get_orderbook("BTCUSDT")
            
            self.test_results['binance_client'] = {
                'status': 'success',
                'details': {
                    'initialized': True,
                    'has_ticker': ticker is not None,
                    'has_orderbook': orderbook is not None,
                    'ticker_price': ticker.get('price') if ticker else None
                }
            }
            
            logger.info("✅ Binance客户端测试成功", **self.test_results['binance_client']['details'])
            
        except Exception as e:
            logger.error("❌ Binance客户端测试失败", error=str(e))
            self.test_results['binance_client'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }

    async def _test_system_monitor(self):
        """测试系统监控"""
        try:
            logger.info("📊 测试系统监控...")
            
            # 创建监控器
            self.system_monitor = SystemMonitor(self.config)
            
            # 添加告警回调
            async def alert_callback(alert_data):
                logger.info("收到测试告警", **alert_data)
            
            self.system_monitor.add_alert_callback(alert_callback)
            
            # 启动监控（短时间测试）
            monitor_task = asyncio.create_task(self.system_monitor.start())
            
            # 等待收集数据
            await asyncio.sleep(5)
            
            # 获取状态
            status = self.system_monitor.get_system_status()
            report = self.system_monitor.get_performance_report(hours=1)
            
            # 停止监控
            await self.system_monitor.stop()
            monitor_task.cancel()
            
            self.test_results['system_monitor'] = {
                'status': 'success',
                'details': {
                    'metrics_collected': status.get('metrics_count', {}),
                    'active_alerts': status.get('active_alerts_count', 0),
                    'has_performance_data': 'system_performance' in report
                }
            }
            
            logger.info("✅ 系统监控测试成功", **self.test_results['system_monitor']['details'])
            
        except Exception as e:
            logger.error("❌ 系统监控测试失败", error=str(e))
            self.test_results['system_monitor'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }

    async def _test_order_manager(self):
        """测试订单管理器"""
        try:
            logger.info("📋 测试订单管理器...")
            
            # 创建订单管理器
            self.order_manager = OrderManager(self.config)
            
            # 设置交易所客户端
            self.order_manager.set_exchange_clients(self.binance_client, self.lighter_client)
            
            # 启动管理器
            await self.order_manager.start()
            
            # 测试下单
            order_request = OrderRequest(
                symbol="BTC/USDT",
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                amount=0.001,
                price=50000.0
            )
            
            order = await self.order_manager.place_order(order_request, "binance")
            
            # 等待订单处理
            await asyncio.sleep(2)
            
            # 测试撤单
            if order.id in self.order_manager.active_orders:
                cancel_success = await self.order_manager.cancel_order(order.id)
            else:
                cancel_success = True  # 订单已完成
            
            # 获取统计
            stats = self.order_manager.get_statistics()
            
            # 停止管理器
            await self.order_manager.stop()
            
            self.test_results['order_manager'] = {
                'status': 'success',
                'details': {
                    'order_placed': order.id is not None,
                    'order_status': order.status.value,
                    'cancel_success': cancel_success,
                    'total_orders': stats.get('total_orders', 0),
                    'success_rate': stats.get('success_rate', 0)
                }
            }
            
            logger.info("✅ 订单管理器测试成功", **self.test_results['order_manager']['details'])
            
        except Exception as e:
            logger.error("❌ 订单管理器测试失败", error=str(e))
            self.test_results['order_manager'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }

    async def _test_performance_optimizer(self):
        """测试性能优化器"""
        try:
            logger.info("⚡ 测试性能优化器...")
            
            # 创建优化器
            self.performance_optimizer = PerformanceOptimizer(self.config)
            
            # 添加优化回调
            async def optimization_callback(optimization_data):
                logger.info("收到优化通知", **optimization_data)
            
            self.performance_optimizer.add_optimization_callback(optimization_callback)
            
            # 启动优化器（短时间测试）
            optimizer_task = asyncio.create_task(self.performance_optimizer.start())
            
            # 等待收集数据
            await asyncio.sleep(5)
            
            # 获取性能摘要
            summary = self.performance_optimizer.get_performance_summary()
            
            # 停止优化器
            await self.performance_optimizer.stop()
            optimizer_task.cancel()
            
            self.test_results['performance_optimizer'] = {
                'status': 'success',
                'details': {
                    'metrics_collected': summary.get('metrics_count', 0),
                    'optimizations_run': summary.get('optimization_count', 0),
                    'has_current_performance': 'current_performance' in summary,
                    'has_average_performance': 'average_performance' in summary
                }
            }
            
            logger.info("✅ 性能优化器测试成功", **self.test_results['performance_optimizer']['details'])
            
        except Exception as e:
            logger.error("❌ 性能优化器测试失败", error=str(e))
            self.test_results['performance_optimizer'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }

    async def _test_integration(self):
        """测试集成功能"""
        try:
            logger.info("🔗 测试集成功能...")
            
            # 测试组件间协作
            integration_tests = {
                'lighter_data_flow': False,
                'monitoring_integration': False,
                'order_execution_flow': False,
                'performance_monitoring': False
            }
            
            # 1. 测试Lighter数据流
            if self.lighter_client and self.lighter_client.get_current_price("BTC/USDT"):
                integration_tests['lighter_data_flow'] = True
            
            # 2. 测试监控集成
            if self.system_monitor:
                # 模拟设置套利引擎引用
                mock_engine = type('MockEngine', (), {
                    'connection_status': {'binance': True, 'lighter': True},
                    'stats': {'total_trades': 5, 'successful_trades': 4},
                    'config': self.config
                })()
                
                self.system_monitor.set_arbitrage_engine(mock_engine)
                integration_tests['monitoring_integration'] = True
            
            # 3. 测试订单执行流程
            if self.order_manager and self.test_results['order_manager']['status'] == 'success':
                integration_tests['order_execution_flow'] = True
            
            # 4. 测试性能监控
            if self.performance_optimizer and self.test_results['performance_optimizer']['status'] == 'success':
                integration_tests['performance_monitoring'] = True
            
            # 计算集成成功率
            success_count = sum(integration_tests.values())
            total_tests = len(integration_tests)
            success_rate = (success_count / total_tests) * 100
            
            self.test_results['integration'] = {
                'status': 'success' if success_rate >= 75 else 'partial',
                'details': {
                    **integration_tests,
                    'success_rate': success_rate,
                    'passed_tests': success_count,
                    'total_tests': total_tests
                }
            }
            
            logger.info("✅ 集成功能测试完成", **self.test_results['integration']['details'])
            
        except Exception as e:
            logger.error("❌ 集成功能测试失败", error=str(e))
            self.test_results['integration'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }

    def _generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        print("\n" + "="*80)
        print("🎯 完整集成测试报告")
        print("="*80)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'success')
        partial_tests = sum(1 for result in self.test_results.values() if result['status'] == 'partial')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'failed')
        
        print(f"\n📈 测试统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过: {passed_tests} ✅")
        print(f"  部分通过: {partial_tests} ⚠️")
        print(f"  失败: {failed_tests} ❌")
        print(f"  成功率: {(passed_tests / total_tests) * 100:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'success' else "⚠️" if result['status'] == 'partial' else "❌"
            print(f"  {status_icon} {test_name}: {result['status']}")
            
            if result['details']:
                for key, value in result['details'].items():
                    print(f"    - {key}: {value}")
        
        print("\n" + "="*80)
        
        # 总结
        if passed_tests == total_tests:
            print("🎉 所有测试通过！系统已准备好投入生产环境。")
        elif passed_tests + partial_tests >= total_tests * 0.8:
            print("⚠️ 大部分测试通过，系统基本可用，建议修复失败的测试。")
        else:
            print("❌ 多个测试失败，建议修复问题后重新测试。")
        
        print("="*80)

    async def cleanup(self):
        """清理资源"""
        try:
            if self.lighter_client:
                await self.lighter_client.close()
            
            if self.binance_client:
                await self.binance_client.close()
            
            if self.system_monitor:
                await self.system_monitor.stop()
            
            if self.order_manager:
                await self.order_manager.stop()
            
            if self.performance_optimizer:
                await self.performance_optimizer.stop()
            
            logger.info("🧹 资源清理完成")
            
        except Exception as e:
            logger.error("清理资源失败", error=str(e))


async def main():
    """主函数"""
    tester = IntegrationTester()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到停止信号，正在清理...")
        asyncio.create_task(tester.cleanup())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await tester.run_complete_test()
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
