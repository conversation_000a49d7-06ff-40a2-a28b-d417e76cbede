#!/usr/bin/env python3
"""
完整套利交易流程测试脚本

测试所有组件的集成和功能：
1. 数据库模块
2. 套利引擎
3. 交易执行
4. Web监控界面
5. 数据持久化
"""

import asyncio
import time
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.database.database import DatabaseManager
from src.database.trade_recorder import TradeRecorder
from src.database.models import TradeRecord, TradeStatus, TradeType, OrderSide, PriceRecord, SpreadRecord, RiskMetrics, PerformanceMetrics
from src.arbitrage.engine import ArbitrageEngine
from src.web.app import create_web_app
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ArbitrageFlowTester:
    """套利交易流程测试器"""
    
    def __init__(self):
        self.db_manager = None
        self.trade_recorder = None
        self.engine = None
        self.web_app = None
        
    async def setup(self):
        """初始化测试环境"""
        logger.info("🚀 开始初始化测试环境...")
        
        # 1. 初始化数据库
        self.db_manager = DatabaseManager("test_arbitrage.db")
        await self.db_manager.initialize()
        logger.info("✅ 数据库初始化完成")
        
        # 2. 初始化交易记录器
        self.trade_recorder = TradeRecorder(self.db_manager)
        logger.info("✅ 交易记录器初始化完成")
        
        # 3. 创建测试配置
        test_config = {
            'trading': {
                'symbol': 'BTC/USDT',
                'paper_trading': True,
                'enabled': True,
                'min_profit_threshold': 10.0,
                'max_trade_amount': 0.1
            },
            'risk_management': {
                'max_position_size': 1000.0,
                'max_daily_loss': 100.0,
                'emergency_stop': False
            },
            'database': {
                'path': 'test_arbitrage.db',
                'backup_dir': 'test_backups'
            },
            'exchanges': {
                'binance': {
                    'api_key': 'test_key',
                    'secret': 'test_secret',
                    'sandbox': True,
                    'testnet': True
                },
                'lighter': {
                    'api_url': 'https://api.lighter.xyz',
                    'private_key': 'test_private_key',
                    'account_index': 595,
                    'api_key_index': 1
                }
            }
        }
        
        # 4. 初始化套利引擎
        self.engine = ArbitrageEngine(test_config)
        # 注入数据库组件
        self.engine.db_manager = self.db_manager
        self.engine.trade_recorder = self.trade_recorder
        
        # 启用纸上交易模式进行测试
        self.engine.is_paper_trading = True
        self.engine.is_trading_enabled = True
        
        logger.info("✅ 套利引擎初始化完成")
        
        # 5. 创建Web应用
        self.web_app = create_web_app(self.engine)
        logger.info("✅ Web应用创建完成")
        
    async def test_database_operations(self):
        """测试数据库操作"""
        logger.info("🧪 开始测试数据库操作...")
        
        # 测试价格记录
        price_record1 = PriceRecord(
            symbol="BTC/USDT",
            exchange="binance",
            price=Decimal("50000.00"),
            volume=Decimal("1.5")
        )
        await self.db_manager.save_price(price_record1)
        
        price_record2 = PriceRecord(
            symbol="BTC/USDT",
            exchange="lighter",
            price=Decimal("50050.00"),
            volume=Decimal("1.2")
        )
        await self.db_manager.save_price(price_record2)
        
        # 测试价差记录
        spread_record = SpreadRecord(
            symbol="BTC/USDT",
            binance_price=Decimal("50000.00"),
            lighter_price=Decimal("50050.00"),
            spread=Decimal("50.00"),
            spread_percentage=Decimal("0.1")
        )
        await self.db_manager.save_spread(spread_record)
        
        # 测试获取历史数据
        price_history = await self.db_manager.get_price_history("BTC/USDT", "binance", 1)
        spread_history = await self.db_manager.get_spread_history("BTC/USDT", 1)
        
        logger.info(f"✅ 价格历史记录数: {len(price_history)}")
        logger.info(f"✅ 价差历史记录数: {len(spread_history)}")
        
    async def test_trade_recording(self):
        """测试交易记录功能"""
        logger.info("🧪 开始测试交易记录功能...")
        
        # 创建测试交易记录
        trade_record = await self.trade_recorder.create_trade_record(
            symbol="BTC/USDT",
            trade_type=TradeType.BUY_BINANCE_SELL_LIGHTER,
            amount=Decimal("0.1"),
            binance_price=Decimal("50000.00"),
            lighter_price=Decimal("50050.00"),
            expected_profit=Decimal("5.00")
        )
        
        logger.info(f"✅ 创建交易记录: {trade_record.trade_id}")
        
        # 模拟订单执行
        await self.trade_recorder.update_binance_order(
            trade_record.trade_id,
            order_id="binance_123456",
            status="filled",
            filled_amount=Decimal("0.1"),
            average_price=Decimal("49995.00")
        )
        
        await self.trade_recorder.update_lighter_order(
            trade_record.trade_id,
            order_id="lighter_789012",
            status="filled",
            filled_amount=Decimal("0.1"),
            average_price=Decimal("50055.00")
        )
        
        # 完成交易
        await self.trade_recorder.complete_trade(
            trade_record.trade_id,
            actual_profit=Decimal("6.00"),
            execution_time=2.5
        )
        
        # 获取活跃交易
        active_trades = await self.trade_recorder.get_active_trades()
        logger.info(f"✅ 活跃交易数量: {len(active_trades)}")
        
        # 计算今日性能
        today = datetime.utcnow()
        daily_performance = await self.trade_recorder.calculate_daily_performance(today)
        logger.info(f"✅ 今日性能: 交易{daily_performance.total_trades}次, 盈利{daily_performance.total_profit}")
        
    async def test_engine_integration(self):
        """测试引擎集成"""
        logger.info("🧪 开始测试引擎集成...")
        
        # 模拟价格数据
        test_prices = {
            'binance': {
                'BTC/USDT': {
                    'bid': 50000.00,
                    'ask': 50010.00,
                    'last': 50005.00
                }
            },
            'lighter': {
                'BTC/USDT': {
                    'bid': 50040.00,
                    'ask': 50060.00,
                    'last': 50050.00
                }
            }
        }
        
        # 模拟引擎状态更新
        if hasattr(self.engine, 'update_prices'):
            await self.engine.update_prices(test_prices)
        
        # 获取引擎状态
        status = self.engine.get_status()
        logger.info(f"✅ 引擎状态: {status}")
        
        # 测试系统监控数据记录
        if hasattr(self.engine, 'record_system_status'):
            await self.engine.record_system_status()
            logger.info("✅ 系统状态记录完成")
        
    async def test_web_api_endpoints(self):
        """测试Web API端点"""
        logger.info("🧪 开始测试Web API端点...")
        
        # 这里我们只是验证API函数能够正常调用
        # 在实际测试中，你可能需要启动Web服务器并发送HTTP请求
        
        try:
            # 模拟API调用
            from fastapi.testclient import TestClient
            client = TestClient(self.web_app)
            
            # 测试状态API
            response = client.get("/api/status")
            logger.info(f"✅ 状态API响应: {response.status_code}")
            
            # 测试交易历史API
            response = client.get("/api/trades")
            logger.info(f"✅ 交易历史API响应: {response.status_code}")
            
            # 测试价格历史API
            response = client.get("/api/prices/history?symbol=BTC/USDT&exchange=binance&hours=1")
            logger.info(f"✅ 价格历史API响应: {response.status_code}")
            
            # 测试价差历史API
            response = client.get("/api/spreads/history?symbol=BTC/USDT&hours=1")
            logger.info(f"✅ 价差历史API响应: {response.status_code}")
            
        except ImportError:
            logger.warning("⚠️ TestClient不可用，跳过API测试")
        except Exception as e:
            logger.error(f"❌ API测试失败: {e}")
    
    async def test_data_persistence(self):
        """测试数据持久化"""
        logger.info("🧪 开始测试数据持久化...")
        
        # 创建大量测试数据
        for i in range(10):
            price_record = PriceRecord(
                symbol="BTC/USDT",
                exchange="binance",
                price=Decimal(f"{50000 + i}.00"),
                volume=Decimal("1.0")
            )
            await self.db_manager.save_price(price_record)
            
            spread_record = SpreadRecord(
                symbol="BTC/USDT",
                binance_price=Decimal(f"{50000 + i}.00"),
                lighter_price=Decimal(f"{50050 + i}.00"),
                spread=Decimal("50.00"),
                spread_percentage=Decimal("0.1")
            )
            await self.db_manager.save_spread(spread_record)
        
        # 测试数据检索
        recent_trades = await self.db_manager.get_recent_trades(5)
        logger.info(f"✅ 最近交易记录数: {len(recent_trades)}")
        
        # 测试数据清理
        old_date = datetime.utcnow() - timedelta(days=31)
        await self.db_manager.cleanup_old_data(old_date)
        logger.info("✅ 数据清理完成")
        
        # 测试数据库备份
        backup_path = await self.db_manager.backup_database()
        logger.info(f"✅ 数据库备份完成: {backup_path}")
        
    async def test_complete_arbitrage_flow(self):
        """测试完整套利流程"""
        logger.info("🧪 开始测试完整套利流程...")
        
        # 模拟套利机会检测
        symbol = "BTC/USDT"
        binance_price = Decimal("50000.00")
        lighter_price = Decimal("50100.00")  # 价差100 USDT
        amount = Decimal("0.1")
        
        # 创建套利交易
        trade_record = await self.trade_recorder.create_trade_record(
            symbol=symbol,
            trade_type=TradeType.BUY_BINANCE_SELL_LIGHTER,
            amount=amount,
            binance_price=binance_price,
            lighter_price=lighter_price,
            expected_profit=Decimal("10.00")  # 预期盈利10 USDT
        )
        
        logger.info(f"✅ 创建套利交易: {trade_record.trade_id}")
        
        # 模拟订单执行过程
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        # Binance买单执行
        await self.trade_recorder.update_binance_order(
            trade_record.trade_id,
            order_id="BIN_" + str(int(time.time())),
            status="filled",
            filled_amount=amount,
            average_price=Decimal("50005.00")  # 实际买入价格
        )
        
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        # Lighter卖单执行
        await self.trade_recorder.update_lighter_order(
            trade_record.trade_id,
            order_id="LIT_" + str(int(time.time())),
            status="filled",
            filled_amount=amount,
            average_price=Decimal("50095.00")  # 实际卖出价格
        )
        
        # 计算实际盈利
        actual_profit = (Decimal("50095.00") - Decimal("50005.00")) * amount
        execution_time = 0.5  # 执行时间0.5秒
        
        # 完成交易
        await self.trade_recorder.complete_trade(
            trade_record.trade_id,
            actual_profit=actual_profit,
            execution_time=execution_time
        )
        
        logger.info(f"✅ 套利交易完成，实际盈利: {actual_profit} USDT")
        
        # 记录风险指标
        risk_metrics = RiskMetrics(
            total_exposure=amount * binance_price,
            max_position_size=Decimal("1000.00"),
            current_drawdown=Decimal("0.00"),
            var_95=Decimal("50.00"),
            sharpe_ratio=Decimal("1.5")
        )
        await self.db_manager.save_risk_metrics(risk_metrics)
        
        # 记录性能指标
        performance = await self.trade_recorder.calculate_daily_performance(datetime.utcnow())
        perf_metrics = PerformanceMetrics(
            period="daily",
            total_trades=performance.total_trades,
            successful_trades=performance.successful_trades,
            failed_trades=performance.failed_trades,
            success_rate=performance.win_rate,
            total_profit=performance.total_profit,
            total_loss=Decimal("0.00"),
            net_profit=performance.total_profit,
            profit_factor=Decimal("1.0"),
            sharpe_ratio=Decimal("1.5"),
            max_drawdown=Decimal("0.00"),
            calmar_ratio=Decimal("1.0"),
            avg_trade_duration_seconds=Decimal("30.0"),
            avg_profit_per_trade=performance.average_profit_per_trade,
            avg_execution_time_ms=Decimal("500.0")
        )
        await self.db_manager.save_performance_metrics(perf_metrics)
        
        logger.info("✅ 完整套利流程测试完成")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🎯 开始运行完整套利交易流程测试")
        
        try:
            await self.setup()
            
            await self.test_database_operations()
            await self.test_trade_recording()
            await self.test_engine_integration()
            await self.test_web_api_endpoints()
            await self.test_data_persistence()
            await self.test_complete_arbitrage_flow()
            
            logger.info("🎉 所有测试完成！")
            
            # 显示测试结果摘要
            await self.show_test_summary()
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            raise
        finally:
            await self.cleanup()
    
    async def show_test_summary(self):
        """显示测试结果摘要"""
        logger.info("📊 测试结果摘要:")
        
        # 获取数据库统计
        recent_trades = await self.db_manager.get_recent_trades(100)
        price_history = await self.db_manager.get_price_history("BTC/USDT", "binance", 24)
        spread_history = await self.db_manager.get_spread_history("BTC/USDT", 24)
        
        logger.info(f"   📈 价格记录数: {len(price_history)}")
        logger.info(f"   📊 价差记录数: {len(spread_history)}")
        logger.info(f"   💰 交易记录数: {len(recent_trades)}")
        
        # 计算总盈利
        total_profit = sum(
            trade.get('actual_profit', 0) or 0 
            for trade in recent_trades 
            if trade.get('status') == 'completed'
        )
        logger.info(f"   💵 总盈利: {total_profit} USDT")
        
        # 成功率
        completed_trades = [t for t in recent_trades if t.get('status') == 'completed']
        successful_trades = [t for t in completed_trades if (t.get('actual_profit') or 0) > 0]
        success_rate = len(successful_trades) / len(completed_trades) * 100 if completed_trades else 0
        logger.info(f"   📈 成功率: {success_rate:.1f}%")
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 清理测试环境...")
        
        if self.db_manager:
            await self.db_manager.close()
        
        # 删除测试数据库文件
        import os
        if os.path.exists("test_arbitrage.db"):
            os.remove("test_arbitrage.db")
            logger.info("✅ 测试数据库文件已删除")


async def main():
    """主函数"""
    tester = ArbitrageFlowTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main()) 