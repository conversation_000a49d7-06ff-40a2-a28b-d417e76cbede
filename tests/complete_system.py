#!/usr/bin/env python3
"""
完整系统集成测试脚本

验证所有模块功能：
1. 配置加载
2. 数据库初始化
3. 套利引擎
4. Web监控界面
5. 数据持久化
6. 交易流程
"""

import asyncio
import sys
import os
import time
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入所有模块
from src.utils.config_loader import ConfigLoader
from src.database.database import DatabaseManager
from src.database.trade_recorder import TradeRecorder
from src.database.models import *
from src.arbitrage.engine import ArbitrageEngine
from src.web.app import create_web_app
from src.utils.logger import setup_logger

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CompleteSystemTester:
    """完整系统测试器"""
    
    def __init__(self):
        self.test_db_path = "test_system.db"
        self.config = None
        self.db_manager = None
        self.trade_recorder = None
        self.engine = None
        self.web_app = None
        self.test_results = {
            'config_loader': False,
            'database_init': False,
            'trade_recorder': False,
            'arbitrage_engine': False,
            'web_app': False,
            'data_persistence': False,
            'complete_trade_flow': False
        }
    
    def print_header(self, title: str):
        """打印测试标题"""
        print("\n" + "="*60)
        print(f"🧪 {title}")
        print("="*60)
    
    def print_result(self, test_name: str, success: bool, message: str = ""):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if message:
            print(f"   {message}")
        self.test_results[test_name] = success
    
    async def test_config_loader(self):
        """测试配置加载器"""
        self.print_header("测试配置加载器")
        
        try:
            # 创建测试配置
            test_config = {
                'trading': {
                    'symbol': 'BTC/USDT',
                    'max_position_size': 0.1,
                    'min_trade_amount': 0.001,
                    'max_trade_amount': 0.01,
                    'ma_period': 20,
                    'min_profit_threshold': 0.001,
                    'max_spread_threshold': 0.02,
                    'paper_trading': True
                },
                'risk_management': {
                    'max_exposure': 1000,
                    'position_limit_pct': 0.8,
                    'stop_loss_pct': 0.05,
                    'max_loss_per_day': 100
                },
                'exchanges': {
                    'binance': {
                        'api_key': 'test_api_key',
                        'secret': 'test_secret',
                        'sandbox': True,
                        'testnet': True
                    },
                    'lighter': {
                        'api_url': 'https://api.lighter.xyz',
                        'private_key': 'test_private_key',
                        'account_index': 595,
                        'api_key_index': 1
                    }
                },
                'database': {
                    'path': self.test_db_path,
                    'backup_dir': 'test_backups'
                },
                'web': {
                    'host': '127.0.0.1',
                    'port': 8001
                }
            }
            
            self.config = test_config
            self.print_result('config_loader', True, "配置创建成功")
            
        except Exception as e:
            self.print_result('config_loader', False, f"配置加载失败: {e}")
    
    async def test_database_init(self):
        """测试数据库初始化"""
        self.print_header("测试数据库初始化")
        
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager(self.test_db_path)
            await self.db_manager.initialize()
            
            # 测试数据库表是否创建成功
            cursor = self.db_manager.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            expected_tables = ['trades', 'prices', 'spreads', 'risk_metrics', 'system_status', 'performance_metrics']
            missing_tables = [table for table in expected_tables if table not in table_names]
            
            if missing_tables:
                self.print_result('database_init', False, f"缺少表: {missing_tables}")
            else:
                self.print_result('database_init', True, f"所有表创建成功: {table_names}")
            
        except Exception as e:
            self.print_result('database_init', False, f"数据库初始化失败: {e}")
    
    async def test_trade_recorder(self):
        """测试交易记录器"""
        self.print_header("测试交易记录器")
        
        try:
            # 初始化交易记录器
            self.trade_recorder = TradeRecorder(self.db_manager)
            
            # 创建测试交易记录
            trade_record = await self.trade_recorder.create_trade_record(
                symbol="BTC/USDT",
                trade_type=TradeType.BUY_ARBITRAGE,
                expected_profit=Decimal("10.50"),
                spread_at_entry=Decimal("0.001"),
                position_size=Decimal("0.1")
            )
            
            # 更新Binance订单
            await self.trade_recorder.update_binance_order(
                trade_record.id,
                "binance_order_123",
                OrderSide.BUY,
                Decimal("50000.00"),
                Decimal("0.1"),
                TradeStatus.EXECUTED,
                Decimal("0.50")
            )
            
            # 更新Lighter订单
            await self.trade_recorder.update_lighter_order(
                trade_record.id,
                "lighter_order_456",
                OrderSide.SELL,
                Decimal("50010.00"),
                Decimal("0.1"),
                TradeStatus.EXECUTED,
                Decimal("0.30")
            )
            
            # 完成交易
            await self.trade_recorder.complete_trade(
                trade_record.id,
                actual_profit=Decimal("9.20"),
                execution_time_ms=1500,
                slippage=Decimal("0.0001")
            )
            
            # 验证交易记录
            recent_trades = await self.db_manager.get_recent_trades(1)
            if recent_trades and recent_trades[0]['id'] == trade_record.id:
                self.print_result('trade_recorder', True, f"交易记录创建成功: {trade_record.id}")
            else:
                self.print_result('trade_recorder', False, "交易记录验证失败")
            
        except Exception as e:
            self.print_result('trade_recorder', False, f"交易记录器测试失败: {e}")
    
    async def test_arbitrage_engine(self):
        """测试套利引擎"""
        self.print_header("测试套利引擎")
        
        try:
            # 初始化套利引擎
            self.engine = ArbitrageEngine(self.config)
            
            # 注入数据库组件
            self.engine.db_manager = self.db_manager
            self.engine.trade_recorder = self.trade_recorder
            
            # 设置为纸上交易模式
            self.engine.is_paper_trading = True
            self.engine.is_trading_enabled = True
            self.engine.is_initialized = True
            
            # 测试引擎状态
            status = self.engine.get_status()
            
            if status and 'is_running' in status:
                self.print_result('arbitrage_engine', True, f"引擎状态获取成功")
            else:
                self.print_result('arbitrage_engine', False, "引擎状态获取失败")
            
        except Exception as e:
            self.print_result('arbitrage_engine', False, f"套利引擎测试失败: {e}")
    
    async def test_web_app(self):
        """测试Web应用"""
        self.print_header("测试Web应用")
        
        try:
            # 创建Web应用
            self.web_app = create_web_app(self.engine)
            
            # 测试基本端点是否存在
            routes = [route.path for route in self.web_app.routes]
            expected_routes = [
                '/',
                '/api/status',
                '/api/trades', 
                '/api/prices',
                '/api/risk'
            ]
            
            missing_routes = [route for route in expected_routes if route not in routes]
            
            if missing_routes:
                self.print_result('web_app', False, f"缺少路由: {missing_routes}")
            else:
                self.print_result('web_app', True, f"所有API路由创建成功")
            
        except Exception as e:
            self.print_result('web_app', False, f"Web应用测试失败: {e}")
    
    async def test_data_persistence(self):
        """测试数据持久化"""
        self.print_header("测试数据持久化")
        
        try:
            # 创建多种类型的测试数据
            
            # 1. 价格记录
            for i in range(5):
                price_record = PriceRecord(
                    timestamp=datetime.utcnow(),
                    symbol="BTC/USDT",
                    exchange="binance",
                    bid_price=Decimal(f"{50000 + i}.00"),
                    ask_price=Decimal(f"{50010 + i}.00"),
                    last_price=Decimal(f"{50005 + i}.00"),
                    volume_24h=Decimal("1000.0"),
                    bid_quantity=Decimal("1.5"),
                    ask_quantity=Decimal("2.0")
                )
                await self.db_manager.save_price(price_record)
            
            # 2. 价差记录
            for i in range(5):
                spread_record = SpreadRecord(
                    timestamp=datetime.utcnow(),
                    symbol="BTC/USDT",
                    binance_bid=Decimal(f"{50000 + i}.00"),
                    binance_ask=Decimal(f"{50010 + i}.00"),
                    lighter_bid=Decimal(f"{50005 + i}.00"),
                    lighter_ask=Decimal(f"{50015 + i}.00"),
                    bid_spread=Decimal(f"{-5 + i}.00"),
                    ask_spread=Decimal(f"{5 - i}.00"),
                    mid_spread=Decimal("0.001"),
                    signal="buy" if i % 2 == 0 else "sell"
                )
                await self.db_manager.save_spread(spread_record)
            
            # 3. 风险指标
            risk_metrics = RiskMetrics(
                timestamp=datetime.utcnow(),
                total_position_size=Decimal("100.0"),
                position_limit=Decimal("1000.0"),
                position_utilization=0.1,
                total_pnl=Decimal("50.0"),
                daily_pnl=Decimal("25.0"),
                max_drawdown=Decimal("10.0"),
                unrealized_pnl=Decimal("5.0"),
                active_trades_count=2,
                pending_orders_count=1,
                failed_trades_count=0,
                success_rate=0.95,
                spread_volatility=0.001,
                price_volatility=0.02,
                liquidity_risk=0.1,
                risk_score=25.0,
                risk_level="LOW"
            )
            await self.db_manager.save_risk_metrics(risk_metrics)
            
            # 4. 系统状态
            system_status = SystemStatus(
                timestamp=datetime.utcnow(),
                is_running=True,
                is_trading_enabled=True,
                is_paper_trading=True,
                binance_connected=True,
                lighter_connected=True,
                websocket_connected=True,
                cpu_usage=15.5,
                memory_usage=45.2,
                network_latency_ms=50.0,
                last_price_update=datetime.utcnow(),
                error_count=0
            )
            await self.db_manager.save_system_status(system_status)
            
            # 验证数据存储
            price_history = await self.db_manager.get_price_history("BTC/USDT", "binance", 1)
            spread_history = await self.db_manager.get_spread_history("BTC/USDT", 1)
            latest_risk = await self.db_manager.get_latest_risk_metrics()
            
            if len(price_history) >= 5 and len(spread_history) >= 5 and latest_risk:
                self.print_result('data_persistence', True, f"数据持久化成功: 价格{len(price_history)}条, 价差{len(spread_history)}条")
            else:
                self.print_result('data_persistence', False, "数据持久化验证失败")
            
        except Exception as e:
            self.print_result('data_persistence', False, f"数据持久化测试失败: {e}")
    
    async def test_complete_trade_flow(self):
        """测试完整交易流程"""
        self.print_header("测试完整交易流程")
        
        try:
            # 模拟完整的套利交易流程
            
            # 1. 创建套利交易
            trade_record = await self.trade_recorder.create_trade_record(
                symbol="BTC/USDT",
                trade_type=TradeType.SELL_ARBITRAGE,
                expected_profit=Decimal("25.50"),
                spread_at_entry=Decimal("0.0015"),
                position_size=Decimal("0.05")
            )
            
            # 2. 模拟价格更新
            await self.trade_recorder.record_price_data(
                symbol="BTC/USDT",
                exchange="binance",
                bid_price=Decimal("50100.00"),
                ask_price=Decimal("50110.00"),
                last_price=Decimal("50105.00")
            )
            
            await self.trade_recorder.record_price_data(
                symbol="BTC/USDT",
                exchange="lighter",
                bid_price=Decimal("50080.00"),
                ask_price=Decimal("50090.00"),
                last_price=Decimal("50085.00")
            )
            
            # 3. 记录价差数据
            await self.trade_recorder.record_spread_data(
                symbol="BTC/USDT",
                binance_bid=Decimal("50100.00"),
                binance_ask=Decimal("50110.00"),
                lighter_bid=Decimal("50080.00"),
                lighter_ask=Decimal("50090.00"),
                signal="sell",
                signal_strength=0.85
            )
            
            # 4. 执行订单
            start_time = time.time()
            
            # Binance卖出订单
            await self.trade_recorder.update_binance_order(
                trade_record.id,
                "binance_sell_789",
                OrderSide.SELL,
                Decimal("50105.00"),
                Decimal("0.05"),
                TradeStatus.EXECUTED,
                Decimal("0.25")
            )
            
            # 等待对冲执行
            await asyncio.sleep(0.1)
            
            # Lighter买入订单（对冲）
            await self.trade_recorder.update_lighter_order(
                trade_record.id,
                "lighter_buy_012",
                OrderSide.BUY,
                Decimal("50085.00"),
                Decimal("0.05"),
                TradeStatus.EXECUTED,
                Decimal("0.15")
            )
            
            # 5. 完成交易
            execution_time = int((time.time() - start_time) * 1000)
            actual_profit = (Decimal("50105.00") - Decimal("50085.00")) * Decimal("0.05") - Decimal("0.40")  # 减去手续费
            
            await self.trade_recorder.complete_trade(
                trade_record.id,
                actual_profit=actual_profit,
                spread_at_exit=Decimal("0.0004"),
                execution_time_ms=execution_time,
                slippage=Decimal("0.0001")
            )
            
            # 6. 计算性能指标
            daily_performance = await self.trade_recorder.calculate_daily_performance(datetime.utcnow())
            
            # 验证完整流程
            final_trades = await self.db_manager.get_recent_trades(10)
            completed_trades = [t for t in final_trades if t.get('status') == 'executed']
            
            if len(completed_trades) >= 2 and daily_performance.total_trades >= 2:
                total_profit = sum(float(t.get('actual_profit', 0)) for t in completed_trades)
                self.print_result('complete_trade_flow', True, 
                               f"完整交易流程成功: {len(completed_trades)}笔交易, 总盈利{total_profit:.2f} USDT")
            else:
                self.print_result('complete_trade_flow', False, "完整交易流程验证失败")
            
        except Exception as e:
            self.print_result('complete_trade_flow', False, f"完整交易流程测试失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("\n🚀 开始完整系统集成测试")
        print("=" * 80)
        
        start_time = time.time()
        
        try:
            # 运行所有测试
            await self.test_config_loader()
            await self.test_database_init()
            await self.test_trade_recorder()
            await self.test_arbitrage_engine()
            await self.test_web_app()
            await self.test_data_persistence()
            await self.test_complete_trade_flow()
            
        except Exception as e:
            logger.error(f"测试执行失败: {e}")
        
        finally:
            await self.cleanup()
        
        # 显示测试结果总结
        execution_time = time.time() - start_time
        self.show_test_summary(execution_time)
    
    def show_test_summary(self, execution_time: float):
        """显示测试结果总结"""
        print("\n" + "="*80)
        print("📊 测试结果总结")
        print("="*80)
        
        passed_tests = sum(1 for result in self.test_results.values() if result)
        total_tests = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name.replace('_', ' ').title()}")
        
        print(f"\n📈 总体结果: {passed_tests}/{total_tests} 测试通过")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！系统完全可用。")
            print("\n💡 下一步建议:")
            print("   1. 运行 python run.py --paper-trading 启动模拟交易")
            print("   2. 访问 http://localhost:8000 查看监控界面")
            print("   3. 配置真实的API密钥后可启动实盘交易")
        else:
            failed_tests = [name for name, result in self.test_results.items() if not result]
            print(f"\n⚠️ 有 {len(failed_tests)} 个测试失败:")
            for test in failed_tests:
                print(f"   - {test}")
            print("\n请检查失败的模块并修复问题后重新测试。")
    
    async def cleanup(self):
        """清理测试资源"""
        try:
            if self.db_manager:
                await self.db_manager.close()
            
            # 删除测试数据库
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
            
            # 清理备份目录
            if os.path.exists("test_backups"):
                import shutil
                shutil.rmtree("test_backups")
            
            logger.info("✅ 测试环境清理完成")
            
        except Exception as e:
            logger.error(f"清理测试环境失败: {e}")


async def main():
    """主函数"""
    tester = CompleteSystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main()) 