#!/usr/bin/env python3
"""
最终监控测试脚本
综合测试系统性能、交易执行和参数优化
"""

import asyncio
import time
import json
import requests
from datetime import datetime
import structlog

logger = structlog.get_logger(__name__)

class FinalMonitoringTest:
    def __init__(self):
        self.api_base = "http://localhost:8000"
        self.test_results = {}
        
    def test_system_connectivity(self):
        """测试系统连接性"""
        logger.info("🔗 测试系统连接性...")
        
        tests = {
            "web_server": False,
            "api_status": False,
            "api_prices": False,
            "binance_connection": False,
            "lighter_connection": False
        }
        
        try:
            # 测试Web服务器
            response = requests.get(f"{self.api_base}/", timeout=5)
            tests["web_server"] = response.status_code == 200
            
            # 测试状态API
            response = requests.get(f"{self.api_base}/api/status", timeout=5)
            if response.status_code == 200:
                tests["api_status"] = True
                status_data = response.json()
                tests["binance_connection"] = status_data.get("connection_status", {}).get("binance", False)
                tests["lighter_connection"] = status_data.get("connection_status", {}).get("lighter", False)
            
            # 测试价格API
            response = requests.get(f"{self.api_base}/api/prices", timeout=5)
            tests["api_prices"] = response.status_code == 200
            
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
        
        self.test_results["connectivity"] = tests
        
        # 输出结果
        for test_name, result in tests.items():
            status = "✅" if result else "❌"
            logger.info(f"   {status} {test_name}: {'通过' if result else '失败'}")
        
        return all(tests.values())
    
    def test_real_time_data_flow(self, duration_seconds=60):
        """测试实时数据流"""
        logger.info(f"📊 测试实时数据流 ({duration_seconds}秒)...")
        
        data_points = []
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        while time.time() < end_time:
            try:
                response = requests.get(f"{self.api_base}/api/prices", timeout=5)
                if response.status_code == 200:
                    price_data = response.json()
                    
                    if not price_data.get('error'):
                        binance_prices = price_data.get('binance_prices', {})
                        lighter_prices = price_data.get('lighter_prices', {})
                        
                        if binance_prices.get('last', 0) > 0 and lighter_prices.get('last', 0) > 0:
                            spread = abs(binance_prices['last'] - lighter_prices['last'])
                            spread_pct = (spread / min(binance_prices['last'], lighter_prices['last'])) * 100
                            
                            data_points.append({
                                'timestamp': time.time(),
                                'binance_price': binance_prices['last'],
                                'lighter_price': lighter_prices['last'],
                                'spread': spread,
                                'spread_pct': spread_pct
                            })
                            
                            logger.debug(f"数据点: 价差 {spread:.2f} USDT ({spread_pct:.3f}%)")
                
            except Exception as e:
                logger.warning(f"数据获取失败: {e}")
            
            time.sleep(2)  # 每2秒检查一次
        
        self.test_results["data_flow"] = {
            "total_points": len(data_points),
            "duration": duration_seconds,
            "avg_interval": duration_seconds / len(data_points) if data_points else 0,
            "data_quality": len(data_points) / (duration_seconds / 2) * 100  # 期望每2秒一个数据点
        }
        
        logger.info(f"✅ 数据流测试完成:")
        logger.info(f"   📊 数据点数量: {len(data_points)}")
        logger.info(f"   📈 数据质量: {self.test_results['data_flow']['data_quality']:.1f}%")
        
        return data_points
    
    def test_arbitrage_opportunity_detection(self, data_points):
        """测试套利机会检测"""
        logger.info("🎯 测试套利机会检测...")
        
        if not data_points:
            logger.warning("没有数据点，无法测试套利机会检测")
            return False
        
        # 分析价差分布
        spreads = [dp['spread_pct'] for dp in data_points]
        avg_spread = sum(spreads) / len(spreads)
        max_spread = max(spreads)
        min_spread = min(spreads)
        
        # 检查是否有超过阈值的价差
        threshold = 0.068  # 从优化后的配置中获取
        opportunities = [s for s in spreads if s >= threshold]
        
        self.test_results["arbitrage_detection"] = {
            "total_data_points": len(data_points),
            "avg_spread": avg_spread,
            "max_spread": max_spread,
            "min_spread": min_spread,
            "threshold": threshold,
            "opportunities_count": len(opportunities),
            "opportunity_rate": len(opportunities) / len(spreads) * 100
        }
        
        logger.info(f"✅ 套利机会检测结果:")
        logger.info(f"   📊 平均价差: {avg_spread:.3f}%")
        logger.info(f"   📈 最大价差: {max_spread:.3f}%")
        logger.info(f"   🎯 套利机会: {len(opportunities)} 次 ({len(opportunities) / len(spreads) * 100:.1f}%)")
        
        return len(opportunities) > 0
    
    def test_system_performance(self):
        """测试系统性能"""
        logger.info("⚡ 测试系统性能...")
        
        # 测试API响应时间
        response_times = []
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(f"{self.api_base}/api/status", timeout=5)
                if response.status_code == 200:
                    response_times.append((time.time() - start_time) * 1000)
            except:
                pass
            time.sleep(0.5)
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        self.test_results["performance"] = {
            "api_response_time_ms": avg_response_time,
            "api_success_rate": len(response_times) / 10 * 100
        }
        
        logger.info(f"✅ 系统性能测试结果:")
        logger.info(f"   ⚡ API响应时间: {avg_response_time:.1f}ms")
        logger.info(f"   📊 API成功率: {len(response_times) / 10 * 100:.1f}%")
        
        return avg_response_time < 500  # 响应时间小于500ms
    
    def test_parameter_optimization_effect(self):
        """测试参数优化效果"""
        logger.info("🎯 测试参数优化效果...")
        
        try:
            # 读取优化历史
            with open("optimization_history.json", "r", encoding="utf-8") as f:
                optimization_history = json.load(f)
            
            if optimization_history:
                latest_optimization = optimization_history[-1]
                
                self.test_results["optimization_effect"] = {
                    "optimization_completed": True,
                    "data_points_analyzed": latest_optimization.get("data_points", 0),
                    "old_threshold": latest_optimization.get("old_thresholds", {}).get("min_spread_pct", "N/A"),
                    "new_threshold": latest_optimization.get("new_thresholds", {}).get("min_spread_pct", "N/A"),
                    "improvement_expected": latest_optimization.get("improvement_expected", False)
                }
                
                logger.info(f"✅ 参数优化效果:")
                logger.info(f"   📊 分析数据点: {latest_optimization.get('data_points', 0)}")
                logger.info(f"   🎯 新阈值: {latest_optimization.get('new_thresholds', {}).get('min_spread_pct', 'N/A')}%")
                
                return True
            else:
                logger.warning("没有找到优化历史")
                return False
                
        except Exception as e:
            logger.error(f"读取优化历史失败: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终测试报告"""
        logger.info("📋 生成最终测试报告...")
        
        # 计算总体评分
        scores = {
            "connectivity": sum(self.test_results.get("connectivity", {}).values()) / 5 * 100,
            "data_flow": min(self.test_results.get("data_flow", {}).get("data_quality", 0), 100),
            "performance": 100 if self.test_results.get("performance", {}).get("api_response_time_ms", 1000) < 500 else 50,
            "optimization": 100 if self.test_results.get("optimization_effect", {}).get("optimization_completed", False) else 0
        }
        
        overall_score = sum(scores.values()) / len(scores)
        
        report = f"""
# 🎯 Binance-Lighter套利交易系统最终测试报告

## 📊 测试执行概要
- **测试时间**: {datetime.now().isoformat()}
- **测试类型**: 综合系统测试
- **总体评分**: {overall_score:.1f}/100

## 🔗 系统连接性测试
- **Web服务器**: {'✅' if self.test_results.get('connectivity', {}).get('web_server') else '❌'}
- **API状态**: {'✅' if self.test_results.get('connectivity', {}).get('api_status') else '❌'}
- **Binance连接**: {'✅' if self.test_results.get('connectivity', {}).get('binance_connection') else '❌'}
- **Lighter连接**: {'✅' if self.test_results.get('connectivity', {}).get('lighter_connection') else '❌'}
- **评分**: {scores['connectivity']:.1f}/100

## 📊 实时数据流测试
- **数据点数量**: {self.test_results.get('data_flow', {}).get('total_points', 0)}
- **数据质量**: {self.test_results.get('data_flow', {}).get('data_quality', 0):.1f}%
- **平均间隔**: {self.test_results.get('data_flow', {}).get('avg_interval', 0):.1f}秒
- **评分**: {scores['data_flow']:.1f}/100

## 🎯 套利机会检测
- **平均价差**: {self.test_results.get('arbitrage_detection', {}).get('avg_spread', 0):.3f}%
- **最大价差**: {self.test_results.get('arbitrage_detection', {}).get('max_spread', 0):.3f}%
- **套利机会**: {self.test_results.get('arbitrage_detection', {}).get('opportunities_count', 0)} 次
- **机会率**: {self.test_results.get('arbitrage_detection', {}).get('opportunity_rate', 0):.1f}%

## ⚡ 系统性能测试
- **API响应时间**: {self.test_results.get('performance', {}).get('api_response_time_ms', 0):.1f}ms
- **API成功率**: {self.test_results.get('performance', {}).get('api_success_rate', 0):.1f}%
- **评分**: {scores['performance']:.1f}/100

## 🎯 参数优化效果
- **优化完成**: {'✅' if self.test_results.get('optimization_effect', {}).get('optimization_completed') else '❌'}
- **新阈值**: {self.test_results.get('optimization_effect', {}).get('new_threshold', 'N/A')}%
- **评分**: {scores['optimization']:.1f}/100

## 🏆 总体评估
{'🟢 优秀' if overall_score >= 80 else '🟡 良好' if overall_score >= 60 else '🔴 需要改进'}

## 💡 建议
1. {'✅ 系统连接稳定，可以进行实盘测试' if scores['connectivity'] >= 80 else '⚠️ 检查系统连接问题'}
2. {'✅ 数据流正常，监控功能完善' if scores['data_flow'] >= 80 else '⚠️ 优化数据流稳定性'}
3. {'✅ 系统性能良好' if scores['performance'] >= 80 else '⚠️ 优化系统性能'}
4. {'✅ 参数优化有效' if scores['optimization'] >= 80 else '⚠️ 重新运行参数优化'}

---
*报告生成时间: {datetime.now().isoformat()}*
"""
        
        with open("final_test_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info("📋 最终测试报告已生成: final_test_report.md")
        logger.info(f"🏆 总体评分: {overall_score:.1f}/100")
        
        return overall_score

async def main():
    """主函数"""
    logger.info("🚀 启动最终监控测试...")
    
    test = FinalMonitoringTest()
    
    try:
        # 1. 测试系统连接性
        connectivity_ok = test.test_system_connectivity()
        
        if not connectivity_ok:
            logger.error("❌ 系统连接测试失败，请检查系统状态")
            return
        
        # 2. 测试实时数据流
        data_points = test.test_real_time_data_flow(duration_seconds=60)
        
        # 3. 测试套利机会检测
        test.test_arbitrage_opportunity_detection(data_points)
        
        # 4. 测试系统性能
        test.test_system_performance()
        
        # 5. 测试参数优化效果
        test.test_parameter_optimization_effect()
        
        # 6. 生成最终报告
        overall_score = test.generate_final_report()
        
        if overall_score >= 80:
            logger.info("🎉 系统测试完成，表现优秀！")
        elif overall_score >= 60:
            logger.info("✅ 系统测试完成，表现良好")
        else:
            logger.warning("⚠️ 系统测试完成，需要改进")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
