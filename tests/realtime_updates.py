#!/usr/bin/env python3
"""
测试实时更新功能
"""

import asyncio
import websockets
import json
import requests
import time
from datetime import datetime

class RealtimeUpdateTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http://", "ws://") + "/ws"
        self.websocket = None
        self.received_messages = []
        
    async def connect_websocket(self):
        """连接WebSocket"""
        try:
            self.websocket = await websockets.connect(self.ws_url)
            print("✅ WebSocket连接成功")
            
            # 订阅实时数据
            await self.websocket.send(json.dumps({"type": "subscribe"}))
            print("📡 已订阅实时数据")
            
            return True
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False
    
    async def listen_for_updates(self, duration=30):
        """监听实时更新"""
        print(f"🔍 开始监听实时更新 ({duration}秒)...")
        
        start_time = time.time()
        message_counts = {
            'price_update': 0,
            'status_update': 0,
            'trade_update': 0,
            'log_update': 0,
            'connection_status': 0
        }
        
        try:
            while time.time() - start_time < duration:
                try:
                    # 设置超时以避免无限等待
                    message = await asyncio.wait_for(
                        self.websocket.recv(), 
                        timeout=1.0
                    )
                    
                    data = json.loads(message)
                    msg_type = data.get('type', 'unknown')
                    
                    if msg_type in message_counts:
                        message_counts[msg_type] += 1
                    
                    self.received_messages.append({
                        'timestamp': time.time(),
                        'type': msg_type,
                        'data': data
                    })
                    
                    # 打印重要消息
                    if msg_type == 'trade_update':
                        trade_data = data.get('data', {})
                        profit = trade_data.get('actual_profit', 0)
                        print(f"💰 收到交易更新: 盈亏 {profit:.4f} USDT")
                    elif msg_type == 'status_update':
                        stats = data.get('data', {}).get('stats', {})
                        total_trades = stats.get('total_trades', 0)
                        total_profit = stats.get('total_profit', 0)
                        print(f"📊 状态更新: 交易 {total_trades} 次, 总盈亏 {total_profit:.4f} USDT")
                    
                except asyncio.TimeoutError:
                    # 超时是正常的，继续监听
                    continue
                except Exception as e:
                    print(f"⚠️ 接收消息错误: {e}")
                    break
        
        except Exception as e:
            print(f"❌ 监听过程中出错: {e}")
        
        print("\n📈 实时更新统计:")
        for msg_type, count in message_counts.items():
            print(f"   {msg_type}: {count} 条消息")
        
        return message_counts
    
    def test_api_endpoints(self):
        """测试API端点的响应时间"""
        print("\n🔍 测试API端点响应时间...")
        
        endpoints = [
            '/api/status',
            '/api/trades?limit=5',
            '/api/prices',
            '/api/logs?limit=10'
        ]
        
        results = {}
        
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    data = response.json()
                    results[endpoint] = {
                        'status': 'success',
                        'response_time_ms': response_time,
                        'data_size': len(str(data))
                    }
                    print(f"   ✅ {endpoint}: {response_time:.1f}ms")
                else:
                    results[endpoint] = {
                        'status': 'error',
                        'status_code': response.status_code
                    }
                    print(f"   ❌ {endpoint}: HTTP {response.status_code}")
                    
            except Exception as e:
                results[endpoint] = {
                    'status': 'error',
                    'error': str(e)
                }
                print(f"   ❌ {endpoint}: {e}")
        
        return results
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 WebSocket连接已关闭")

async def main():
    """主测试函数"""
    print("🚀 开始测试实时更新功能...")
    print("=" * 50)
    
    tester = RealtimeUpdateTester()
    
    # 测试API端点
    api_results = tester.test_api_endpoints()
    
    # 连接WebSocket
    if await tester.connect_websocket():
        # 监听实时更新
        message_counts = await tester.listen_for_updates(duration=30)
        
        # 分析结果
        print("\n" + "=" * 50)
        print("📋 测试结果分析:")
        
        # 检查实时更新频率
        if message_counts['price_update'] > 0:
            print("✅ 价格数据实时更新正常")
        else:
            print("❌ 价格数据实时更新异常")
        
        if message_counts['status_update'] > 0:
            print("✅ 状态数据实时更新正常")
        else:
            print("❌ 状态数据实时更新异常")
        
        if message_counts['trade_update'] > 0:
            print("✅ 交易记录实时推送正常")
        else:
            print("⚠️ 测试期间无新交易（正常情况）")
        
        # 计算更新频率
        if message_counts['price_update'] > 0:
            price_freq = message_counts['price_update'] / 30
            print(f"📊 价格更新频率: {price_freq:.1f} 次/秒")
        
        if message_counts['status_update'] > 0:
            status_freq = message_counts['status_update'] / 30
            print(f"📊 状态更新频率: {status_freq:.1f} 次/秒")
        
        await tester.close()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
