# 🧪 测试脚本目录

这个目录包含了所有的测试和验证脚本，用于验证系统功能和性能。

## 📋 测试脚本列表

### 🎭 演示脚本
- **`demo.py`** - 完整系统演示脚本
  - 展示系统的主要功能
  - 适合新用户了解系统能力
  - 包含模拟交易演示

### 📊 监控测试
- **`monitoring_test.py`** - 系统监控功能测试
  - 测试Web界面功能
  - 验证实时数据更新
  - 检查监控指标准确性

- **`playwright_monitoring.py`** - Playwright自动化监控测试
  - 浏览器自动化测试
  - Web界面交互测试
  - 截图和报告生成

### ✅ 验证脚本
- **`playwright_verification.py`** - Playwright验证脚本
  - 自动化功能验证
  - 端到端测试
  - 性能基准测试

### 🔧 系统测试
- **`complete_system.py`** - 完整系统测试
  - 全面的系统功能测试
  - 集成测试
  - 性能压力测试

- **`complete_flow.py`** - 完整流程测试
  - 端到端交易流程测试
  - 数据流验证
  - 错误处理测试

- **`integration_test.py`** - 集成测试
  - 模块间集成测试
  - API接口测试
  - 数据库集成测试

### 📈 性能测试
- **`parameter_optimization.py`** - 参数优化测试
  - 交易参数优化
  - 性能调优
  - 基准测试

- **`realtime_updates.py`** - 实时更新测试
  - WebSocket连接测试
  - 实时数据流测试
  - 延迟测试

- **`web_stats.py`** - Web统计测试
  - Web界面统计功能测试
  - API响应测试
  - 数据准确性验证

## 🚀 使用方法

### 运行单个测试
```bash
# 运行演示脚本
python tests/demo.py

# 运行监控测试
python tests/monitoring_test.py

# 运行Playwright测试
python tests/playwright_verification.py
```

### 运行完整测试套件
```bash
# 运行所有测试
python tests/complete_system.py

# 运行集成测试
python tests/integration_test.py
```

### 性能测试
```bash
# 参数优化
python tests/parameter_optimization.py

# 实时性能测试
python tests/realtime_updates.py
```

## 📊 测试报告

测试脚本会生成以下类型的报告：

- **JSON报告**: 详细的测试结果数据
- **Markdown报告**: 人类可读的测试总结
- **截图**: Playwright测试的可视化结果
- **性能指标**: 系统性能基准数据

## ⚠️ 注意事项

1. **模拟交易模式**: 所有测试默认在模拟交易模式下运行
2. **网络连接**: 测试需要稳定的网络连接
3. **API配置**: 确保API配置正确
4. **系统资源**: 某些测试可能消耗较多系统资源

## 🔧 故障排除

如果测试失败：

1. 检查网络连接
2. 验证API配置
3. 查看测试日志
4. 确认系统依赖

## 📞 获取帮助

如果遇到测试问题：

1. 查看测试脚本的注释
2. 检查系统日志
3. 参考主文档
4. 运行系统诊断

---

**最后更新**: 2025-05-31
**测试版本**: v2.2.0
