#!/usr/bin/env python3
"""
测试Web界面统计数据显示
"""

import requests
import json
import time

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试API端点...")
    
    # 测试状态端点
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            data = response.json()
            stats = data.get('stats', {})
            
            print("✅ /api/status 端点正常")
            print(f"   📊 总交易次数: {stats.get('total_trades', 0)}")
            print(f"   💰 总盈亏: {stats.get('total_profit', 0):.2f} USDT")
            print(f"   📈 成功率: {stats.get('win_rate', 0):.1f}%")
            print(f"   🏃 运行状态: {data.get('is_running', False)}")
            
            # 检查统计数据是否为0
            if stats.get('total_trades', 0) == 0:
                print("❌ 总交易次数为0 - 需要修复")
                return False
            else:
                print("✅ 总交易次数正常")
                
        else:
            print(f"❌ /api/status 端点错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ /api/status 端点异常: {e}")
        return False
    
    # 测试交易记录端点
    try:
        response = requests.get(f"{base_url}/api/trades")
        if response.status_code == 200:
            data = response.json()
            trades = data.get('trades', [])
            
            print("✅ /api/trades 端点正常")
            print(f"   📝 交易记录数量: {len(trades)}")
            
            if len(trades) > 0:
                latest_trade = trades[0]
                print(f"   🕐 最新交易时间: {latest_trade.get('timestamp', 'N/A')}")
                print(f"   💵 最新交易盈亏: {latest_trade.get('actual_profit', 0):.4f} USDT")
            
        else:
            print(f"❌ /api/trades 端点错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ /api/trades 端点异常: {e}")
    
    return True

def test_web_interface():
    """测试Web界面"""
    base_url = "http://localhost:8000"
    
    print("\n🌐 测试Web界面...")
    
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✅ Web界面可访问")
            
            # 检查关键内容
            content = response.text
            if "套利交易监控系统" in content:
                print("✅ 页面标题正确")
            if "总交易次数" in content:
                print("✅ 统计区域存在")
            if "交易记录" in content:
                print("✅ 交易记录区域存在")
            if "系统日志" in content:
                print("✅ 系统日志区域存在")
                
        else:
            print(f"❌ Web界面错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web界面异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始测试Web界面统计数据显示...")
    print("=" * 50)
    
    # 测试API端点
    api_ok = test_api_endpoints()
    
    # 测试Web界面
    web_ok = test_web_interface()
    
    print("\n" + "=" * 50)
    if api_ok and web_ok:
        print("🎉 所有测试通过！统计数据显示正常")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
