#!/usr/bin/env python3
"""
Playwright自动化监控测试脚本
用于观察交易执行、监控性能并调整参数
"""

import asyncio
import time
import json
from datetime import datetime
from playwright.async_api import async_playwright
import structlog

logger = structlog.get_logger(__name__)

class ArbitrageMonitoringTest:
    def __init__(self):
        self.browser = None
        self.page = None
        self.monitoring_data = []
        self.test_results = {}
        
    async def setup_browser(self):
        """设置浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口
            args=['--start-maximized']
        )
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
    async def navigate_to_dashboard(self):
        """导航到监控仪表板"""
        try:
            logger.info("🌐 导航到监控仪表板...")
            await self.page.goto("http://localhost:8000", wait_until="networkidle")
            
            # 等待页面加载完成
            await self.page.wait_for_selector(".container-fluid", timeout=10000)
            logger.info("✅ 监控仪表板加载成功")
            
            # 截图记录
            await self.page.screenshot(path="screenshots/dashboard_loaded.png")
            
        except Exception as e:
            logger.error(f"❌ 导航失败: {e}")
            raise
            
    async def monitor_real_time_data(self, duration_minutes=5):
        """监控实时数据"""
        logger.info(f"📊 开始监控实时数据 ({duration_minutes}分钟)...")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        monitoring_results = {
            "price_updates": [],
            "system_status": [],
            "connection_status": [],
            "spread_data": [],
            "trade_signals": []
        }
        
        while time.time() < end_time:
            try:
                # 检查连接状态
                connection_status = await self.check_connection_status()
                monitoring_results["connection_status"].append({
                    "timestamp": datetime.now().isoformat(),
                    "status": connection_status
                })
                
                # 获取价格数据
                price_data = await self.get_price_data()
                if price_data:
                    monitoring_results["price_updates"].append({
                        "timestamp": datetime.now().isoformat(),
                        "data": price_data
                    })
                
                # 检查系统状态
                system_status = await self.get_system_status()
                if system_status:
                    monitoring_results["system_status"].append({
                        "timestamp": datetime.now().isoformat(),
                        "status": system_status
                    })
                
                # 检查价差数据
                spread_data = await self.get_spread_data()
                if spread_data:
                    monitoring_results["spread_data"].append({
                        "timestamp": datetime.now().isoformat(),
                        "spread": spread_data
                    })
                
                # 等待5秒再次检查
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"监控过程中出错: {e}")
                continue
        
        self.test_results["monitoring"] = monitoring_results
        logger.info("✅ 实时数据监控完成")
        
        return monitoring_results
    
    async def check_connection_status(self):
        """检查连接状态"""
        try:
            # 查找连接状态指示器
            status_element = await self.page.query_selector("#connectionStatus")
            if status_element:
                status_text = await status_element.text_content()
                return status_text.strip()
            return "未知"
        except:
            return "检查失败"
    
    async def get_price_data(self):
        """获取价格数据"""
        try:
            # 查找价格显示元素
            binance_price = await self.page.query_selector("#binancePrice")
            lighter_price = await self.page.query_selector("#lighterPrice")
            
            price_data = {}
            if binance_price:
                price_data["binance"] = await binance_price.text_content()
            if lighter_price:
                price_data["lighter"] = await lighter_price.text_content()
                
            return price_data if price_data else None
        except:
            return None
    
    async def get_system_status(self):
        """获取系统状态"""
        try:
            # 查找系统状态元素
            status_elements = await self.page.query_selector_all(".status-card")
            status_data = {}
            
            for element in status_elements:
                title = await element.query_selector(".card-title")
                value = await element.query_selector(".card-text")
                
                if title and value:
                    title_text = await title.text_content()
                    value_text = await value.text_content()
                    status_data[title_text.strip()] = value_text.strip()
            
            return status_data if status_data else None
        except:
            return None
    
    async def get_spread_data(self):
        """获取价差数据"""
        try:
            # 查找价差显示元素
            spread_element = await self.page.query_selector("#currentSpread")
            if spread_element:
                spread_text = await spread_element.text_content()
                return spread_text.strip()
            return None
        except:
            return None
    
    async def test_web_interface_functionality(self):
        """测试Web界面功能"""
        logger.info("🧪 测试Web界面功能...")
        
        test_results = {
            "navigation": False,
            "real_time_updates": False,
            "api_endpoints": False,
            "websocket_connection": False
        }
        
        try:
            # 测试导航
            await self.page.reload()
            await self.page.wait_for_selector(".container-fluid", timeout=10000)
            test_results["navigation"] = True
            logger.info("✅ 页面导航测试通过")
            
            # 测试API端点
            api_response = await self.page.evaluate("""
                async () => {
                    try {
                        const response = await fetch('/api/status');
                        return response.ok;
                    } catch (e) {
                        return false;
                    }
                }
            """)
            test_results["api_endpoints"] = api_response
            logger.info(f"{'✅' if api_response else '❌'} API端点测试")
            
            # 测试实时更新
            initial_time = await self.page.text_content("#lastUpdate") if await self.page.query_selector("#lastUpdate") else None
            await asyncio.sleep(10)
            updated_time = await self.page.text_content("#lastUpdate") if await self.page.query_selector("#lastUpdate") else None
            
            test_results["real_time_updates"] = initial_time != updated_time
            logger.info(f"{'✅' if test_results['real_time_updates'] else '❌'} 实时更新测试")
            
        except Exception as e:
            logger.error(f"Web界面功能测试失败: {e}")
        
        self.test_results["web_interface"] = test_results
        return test_results
    
    async def analyze_arbitrage_opportunities(self):
        """分析套利机会"""
        logger.info("📈 分析套利机会...")
        
        opportunities = []
        
        try:
            # 获取当前价格数据
            price_data = await self.get_price_data()
            if price_data and "binance" in price_data and "lighter" in price_data:
                # 解析价格（假设格式为 "价格 USDT"）
                binance_price_str = price_data["binance"].replace("USDT", "").replace(",", "").strip()
                lighter_price_str = price_data["lighter"].replace("USDT", "").replace(",", "").strip()
                
                try:
                    binance_price = float(binance_price_str)
                    lighter_price = float(lighter_price_str)
                    
                    # 计算价差
                    spread = abs(binance_price - lighter_price)
                    spread_percentage = (spread / min(binance_price, lighter_price)) * 100
                    
                    opportunity = {
                        "timestamp": datetime.now().isoformat(),
                        "binance_price": binance_price,
                        "lighter_price": lighter_price,
                        "spread": spread,
                        "spread_percentage": spread_percentage,
                        "profitable": spread_percentage > 0.1  # 假设0.1%以上为有利可图
                    }
                    
                    opportunities.append(opportunity)
                    logger.info(f"💰 发现套利机会: 价差 {spread:.2f} USDT ({spread_percentage:.3f}%)")
                    
                except ValueError:
                    logger.warning("价格数据解析失败")
        
        except Exception as e:
            logger.error(f"套利机会分析失败: {e}")
        
        self.test_results["arbitrage_opportunities"] = opportunities
        return opportunities
    
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("📋 生成测试报告...")
        
        report = {
            "test_timestamp": datetime.now().isoformat(),
            "test_duration": "5分钟监控测试",
            "results": self.test_results,
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0
            }
        }
        
        # 计算测试统计
        if "web_interface" in self.test_results:
            for test_name, result in self.test_results["web_interface"].items():
                report["summary"]["total_tests"] += 1
                if result:
                    report["summary"]["passed_tests"] += 1
                else:
                    report["summary"]["failed_tests"] += 1
        
        # 保存报告
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ 测试报告已保存到 test_report.json")
        return report
    
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        logger.info("🧹 资源清理完成")

async def main():
    """主函数"""
    logger.info("🚀 启动Playwright监控测试...")
    
    test = ArbitrageMonitoringTest()
    
    try:
        # 设置浏览器
        await test.setup_browser()
        
        # 导航到仪表板
        await test.navigate_to_dashboard()
        
        # 监控实时数据
        await test.monitor_real_time_data(duration_minutes=2)  # 2分钟测试
        
        # 测试Web界面功能
        await test.test_web_interface_functionality()
        
        # 分析套利机会
        await test.analyze_arbitrage_opportunities()
        
        # 生成测试报告
        await test.generate_test_report()
        
        logger.info("🎉 监控测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
