# 模拟交易模式开发指导

## 🎯 核心原则

### ⚠️ **重要澄清：模拟交易的范围**

**模拟交易模式下，只有交易操作是模拟的，其他所有功能都是真实的。**

## 📋 功能分类

### ✅ **真实功能（Paper Trading模式下保持真实）**

#### 1. **市场数据获取**
- ✅ Binance WebSocket价格流 - **真实**
- ✅ Lighter WebSocket价格流 - **真实**
- ✅ 订单簿数据 - **真实**
- ✅ 成交数据 - **真实**
- ✅ 市场深度 - **真实**

#### 2. **数据分析与计算**
- ✅ 价差计算 - **真实**
- ✅ 移动平均线计算 - **真实**
- ✅ 套利信号生成 - **真实**
- ✅ 技术指标分析 - **真实**

#### 3. **风险管理**
- ✅ 风险指标计算 - **真实**
- ✅ 仓位监控 - **真实**（基于模拟仓位）
- ✅ 风险阈值检查 - **真实**
- ✅ 止损逻辑 - **真实**

#### 4. **系统监控**
- ✅ 连接状态监控 - **真实**
- ✅ 系统性能监控 - **真实**
- ✅ 日志记录 - **真实**
- ✅ Web UI显示 - **真实**

#### 5. **数据持久化**
- ✅ 价格历史记录 - **真实**
- ✅ 交易记录 - **真实**（标记为模拟）
- ✅ 系统状态记录 - **真实**
- ✅ 风险指标记录 - **真实**

### 🎭 **模拟功能（Paper Trading模式下的模拟部分）**

#### 1. **交易执行**
- 🎭 订单提交 - **模拟**
- 🎭 订单成交 - **模拟**
- 🎭 订单取消 - **模拟**
- 🎭 资金变动 - **模拟**

#### 2. **账户操作**
- 🎭 余额扣减 - **模拟**
- 🎭 手续费计算 - **模拟**
- 🎭 仓位变更 - **模拟**

## 🏗️ **技术实现指导**

### 1. **交易所客户端设计**

```python
class ExchangeClient:
    def __init__(self, is_paper_trading=False):
        self.is_paper_trading = is_paper_trading
        # 价格数据连接 - 始终真实
        self.websocket_connection = self._setup_websocket()
        # 交易连接 - 根据模式决定
        self.trading_connection = self._setup_trading_connection()
    
    async def get_orderbook(self):
        """获取订单簿 - 始终真实"""
        return await self._fetch_real_orderbook()
    
    async def place_order(self, ...):
        """下单 - 根据模式决定"""
        if self.is_paper_trading:
            return self._simulate_order(...)
        else:
            return await self._place_real_order(...)
```

### 2. **数据流处理**

```python
class MarketDataHandler:
    def __init__(self, is_paper_trading=False):
        # 市场数据处理逻辑完全相同
        # 不受paper_trading模式影响
        pass
    
    def process_price_update(self, price_data):
        """价格更新处理 - 始终真实"""
        # 更新策略价格
        # 触发信号计算
        # 记录价格历史
```

### 3. **风险管理**

```python
class RiskManager:
    def __init__(self, is_paper_trading=False):
        self.is_paper_trading = is_paper_trading
        # 风险计算逻辑完全相同
        
    def check_risk(self, positions):
        """风险检查 - 始终真实逻辑"""
        # 基于当前仓位（真实或模拟）进行真实的风险计算
        return self._calculate_risk_metrics(positions)
```

## 🐛 **常见问题与解决方案**

### 问题1：模拟模式下没有价格数据
**❌ 错误理解**: 认为模拟模式需要生成模拟价格数据
**✅ 正确理解**: 模拟模式使用真实价格数据，只是不执行真实交易

### 问题2：WebSocket连接在模拟模式下断开
**❌ 错误实现**: 在模拟模式下禁用WebSocket连接
**✅ 正确实现**: 模拟模式下保持所有数据连接正常

### 问题3：风险管理在模拟模式下不工作
**❌ 错误实现**: 在模拟模式下跳过风险检查
**✅ 正确实现**: 基于模拟仓位进行真实的风险计算

## 📊 **数据标识规范**

### 交易记录标识
```python
trade_record = {
    'id': 'trade_12345',
    'is_simulation': True,  # 标识这是模拟交易
    'real_prices': True,    # 但使用的是真实价格
    'execution_type': 'paper_trading',
    # ... 其他字段使用真实数据
}
```

### 日志记录标识
```python
logger.info("模拟交易执行", 
           order_id="paper_123",
           real_price=45000.123,  # 真实价格
           simulated_execution=True)  # 模拟执行
```

## 🎯 **开发检查清单**

在开发Paper Trading功能时，请确认：

- [ ] ✅ 价格数据获取是否正常工作？
- [ ] ✅ WebSocket连接是否建立并接收数据？
- [ ] ✅ 价差分析是否基于真实价格？
- [ ] ✅ 风险管理是否正常计算？
- [ ] ✅ UI是否显示真实的市场数据？
- [ ] 🎭 交易执行是否正确模拟？
- [ ] 🎭 账户余额是否正确模拟？
- [ ] 📊 交易记录是否正确标识为模拟？

## 🔍 **调试指导**

### 当UI显示空数据时检查：
1. **API端点是否返回真实数据？** (`/api/prices`, `/api/status`)
2. **WebSocket连接是否正常？** （检查浏览器控制台）
3. **交易所客户端是否正确初始化？** （检查连接状态）
4. **价格数据回调是否正确处理？** （检查日志）

### 排查步骤：
```bash
# 1. 检查API端点
curl http://localhost:8000/api/prices

# 2. 检查系统状态
curl http://localhost:8000/api/status

# 3. 检查日志
tail -f logs/arbitrage.log | grep -E "(价格|WebSocket|连接)"
```

---

**记住：Paper Trading = 真实数据 + 模拟交易**

## 🔧 **关键问题修复记录**

### 🐛 **UI显示空数据问题（2025-05-29修复）**

#### **问题描述**
在Paper Trading模式下，Web UI显示所有价格数据为"--"，无法看到真实的市场数据。

#### **根本原因分析**
**错误实现**: 在`src/exchanges/binance_client.py`和`src/exchanges/lighter_client.py`中，当`is_paper_trading=True`时，两个客户端都跳过了WebSocket订阅：

```python
# ❌ 错误的实现
if self.is_paper_trading:
    logger.info("模拟交易模式 - 跳过订阅", symbol=symbol)
    return  # 这里直接返回，没有订阅价格数据
```

**这违反了Paper Trading的核心原则**: 只有交易操作应该是模拟的，价格数据获取应该是真实的。

#### **修复方案**

**1. Binance客户端修复**
```python
# ✅ 正确的实现
async def subscribe_orderbook(self, symbol: str) -> None:
    """订阅订单簿WebSocket"""
    try:
        # 注意：模拟交易模式下仍然需要真实的价格数据
        # 只有交易执行是模拟的，价格数据获取是真实的
        
        # 转换符号格式并启动WebSocket连接
        ws_symbol = symbol.replace('/', '').lower()
        stream = f"{ws_symbol}@depth20@100ms"
        asyncio.create_task(self._start_orderbook_ws(stream, symbol))
        
        mode_info = "(模拟交易模式-真实数据)" if self.is_paper_trading else ""
        logger.info("订阅订单簿成功", symbol=symbol, stream=stream, mode=mode_info)
        
    except Exception as e:
        logger.error("订阅订单簿失败", symbol=symbol, error=str(e))
        raise
```

**2. WebSocket URL配置修复**
```python
# ✅ 修复WebSocket URL配置
if self.is_paper_trading:
    self.ws_url = "wss://stream.binance.com:9443"  # 使用生产环境获取真实数据
    logger.info("模拟交易模式 - 使用生产环境WebSocket获取真实价格数据")
else:
    self.ws_url = "wss://testnet.binance.vision/ws-api/v3" if testnet else "wss://stream.binance.com:9443"
```

**3. WebSocket连接URL格式修复**
```python
# ✅ 正确的WebSocket连接格式
async def _start_orderbook_ws(self, stream: str, symbol: str) -> None:
    """启动订单簿WebSocket连接"""
    url = f"{self.ws_url}/ws/{stream}"  # 正确的格式：/ws/streamName
    # ...rest of the code
```

**4. Lighter客户端解决方案**
对于Lighter客户端，如果没有真实的API访问权限，可以提供模拟价格数据生成器：

```python
# ✅ Lighter客户端的解决方案
async def subscribe_orderbook(self, symbol: str) -> None:
    """订阅订单簿WebSocket"""
    try:
        if self.is_paper_trading:
            # 在模拟交易模式下，如果没有真实的Lighter API，
            # 创建一个模拟的价格生成器来提供数据
            logger.info("模拟交易模式 - 启动Lighter模拟价格生成器", symbol=symbol)
            asyncio.create_task(self._start_mock_price_generator(symbol))
            return
        
        # 真实模式下的WebSocket订阅逻辑
        # ...
```

#### **修复结果**
- ✅ Binance价格数据：现在应该显示真实数据
- ✅ Lighter价格数据：显示模拟生成的合理价格数据  
- ✅ 价差计算：基于真实/模拟数据进行计算
- ✅ UI显示：所有价格字段正常显示，不再是"--"

#### **测试验证**
```bash
# 启动模拟交易模式
python3 run.py --paper-trading

# 检查价格API返回
curl -s http://localhost:8000/api/prices

# 预期结果示例：
{
  "binance_prices": {"bid": 97234.12, "ask": 97245.67, "last": 97239.89},
  "lighter_prices": {"bid": 97235.45, "ask": 97246.78, "last": 97241.12},
  "spread_ma_value": 0.0001234,
  "current_signal": "HOLD",
  "timestamp": 1748490429.047671
}
```

---

**记住：Paper Trading = 真实数据 + 模拟交易**

## 🎯 **开发最佳实践**

### 1. **模式区分原则**
- **数据层**: 永远使用真实数据（价格、订单簿、成交记录）
- **计算层**: 使用真实数据进行分析计算
- **执行层**: 只在交易执行时区分真实/模拟

### 2. **代码示例**
```python
class ArbitrageEngine:
    def __init__(self, is_paper_trading=False):
        self.is_paper_trading = is_paper_trading
        
    async def execute_trade(self, signal):
        # 数据分析和信号生成 - 永远真实
        spread = self.calculate_spread()  # 真实数据
        risk_score = self.assess_risk()   # 真实计算
        
        if signal.should_trade():
            if self.is_paper_trading:
                # 🎭 模拟交易执行
                result = await self._simulate_trade(signal)
            else:
                # ✅ 真实交易执行  
                result = await self._execute_real_trade(signal)
            
            # 记录和监控 - 永远真实
            await self.record_trade(result, is_simulated=self.is_paper_trading)
```

### 3. **配置管理**
```yaml
# config/settings.yaml
paper_trading:
  enabled: true
  use_real_data: true     # ✅ 永远为true
  simulate_trades: true   # 🎭 交易执行模拟
  mock_balances:
    BTC: 1.0
    USDT: 50000.0
```

### 4. **日志标识**
```python
# 在日志中清楚标识模式
logger.info("执行交易", 
           mode="PAPER_TRADING" if self.is_paper_trading else "LIVE",
           data_source="REAL", 
           execution="SIMULATED" if self.is_paper_trading else "REAL")
```

## 📚 **相关文档**
- [Paper Trading用户指南](USER_GUIDE.md#paper-trading)
- [WebSocket连接配置](WEBSOCKET_CONFIG.md)
- [价格数据处理流程](PRICE_DATA_FLOW.md)
- [风险管理系统](RISK_MANAGEMENT.md) 