# 🔒 安全配置指南

## 📋 概述

本指南详细说明如何安全地配置和管理 Binance-Lighter 套利交易系统中的敏感信息，包括API密钥、私钥和其他机密配置。

## ⚠️ 安全原则

### 核心安全原则
1. **永远不要将API密钥提交到版本控制系统**
2. **使用强密码和复杂的私钥**
3. **定期更换API密钥**
4. **启用所有可用的安全功能**
5. **监控API使用情况**

### 最小权限原则
- 只授予系统运行所需的最小权限
- 定期审核API权限设置
- 使用IP白名单限制访问

## 🔑 API密钥管理

### Binance API 配置

#### 1. 创建API密钥
1. 登录 Binance 账户
2. 前往 **账户管理** > **API管理**
3. 创建新的API密钥
4. **重要**：仅启用必要的权限
   - ✅ 启用：现货交易
   - ✅ 启用：账户信息读取
   - ❌ 禁用：提币权限
   - ❌ 禁用：期货交易（除非需要）

#### 2. 安全设置
```yaml
binance:
  api_key: "YOUR_API_KEY"
  secret: "YOUR_SECRET_KEY"
  sandbox: true        # 测试环境，生产环境设为 false
  testnet: true        # 测试网络，生产环境设为 false
```

#### 3. IP白名单设置
- 在 Binance API 管理页面设置IP白名单
- 只允许服务器IP访问
- 定期检查和更新IP列表

### Lighter API 配置

#### 1. 私钥安全
```yaml
lighter:
  private_key: "YOUR_PRIVATE_KEY"  # 32字节十六进制私钥
  account_index: 595
  api_key_index: 1
```

#### 2. 私钥生成最佳实践
- 使用安全的随机数生成器
- 确保私钥具有足够的熵
- 永远不要重复使用私钥
- 定期轮换私钥

## 📁 配置文件管理

### 配置文件结构
```
config/
├── settings.yaml           # ✅ 可提交（无敏感信息）
├── exchanges.yaml.template # ✅ 可提交（模板文件）
├── exchanges.yaml          # ❌ 不可提交（包含密钥）
└── secrets.yaml            # ❌ 不可提交（敏感配置）
```

### 首次配置步骤

#### 1. 复制模板文件
```bash
# 复制交易所配置模板
cp config/exchanges.yaml.template config/exchanges.yaml
```

#### 2. 填入真实配置
编辑 `config/exchanges.yaml`，填入您的真实API密钥：

```yaml
binance:
  api_key: "your_real_binance_api_key"
  secret: "your_real_binance_secret_key"
  sandbox: true  # 生产环境改为 false

lighter:
  private_key: "your_real_lighter_private_key"
  account_index: 595
  api_key_index: 1
```

#### 3. 验证配置
```bash
# 运行配置验证
python run.py --test-system

# 检查API连接
python run.py --dry-run
```

## 🛡️ 环境变量管理

### 使用环境变量（推荐）

创建 `.env` 文件（已被 .gitignore 忽略）：
```bash
# .env 文件
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
LIGHTER_PRIVATE_KEY=your_lighter_private_key
```

### Python代码中读取环境变量
```python
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 读取配置
binance_api_key = os.getenv('BINANCE_API_KEY')
binance_secret = os.getenv('BINANCE_SECRET_KEY')
lighter_private_key = os.getenv('LIGHTER_PRIVATE_KEY')
```

## 🔍 安全检查清单

### 部署前检查
- [ ] API密钥已从代码中移除
- [ ] 配置文件已添加到 .gitignore
- [ ] 环境变量已正确设置
- [ ] IP白名单已配置
- [ ] API权限已限制到最小
- [ ] 测试环境配置已验证

### 运行时监控
- [ ] 监控API使用频率
- [ ] 检查异常登录活动
- [ ] 监控账户余额变化
- [ ] 设置异常交易告警
- [ ] 定期检查系统日志

### 定期维护
- [ ] 每月更换API密钥
- [ ] 检查IP白名单有效性
- [ ] 审核API权限设置
- [ ] 更新安全补丁
- [ ] 备份重要配置

## 🚨 安全事件响应

### 发现密钥泄露时
1. **立即撤销泄露的API密钥**
2. **生成新的API密钥**
3. **检查账户交易记录**
4. **更改所有相关密码**
5. **通知相关人员**
6. **分析泄露原因**

### 异常活动检测
- 监控非预期的交易活动
- 检查API调用频率异常
- 关注账户余额异常变化
- 设置实时告警机制

## 📚 安全工具推荐

### 密钥管理工具
- **1Password / Bitwarden**: 密码管理
- **HashiCorp Vault**: 企业级密钥管理
- **AWS Secrets Manager**: 云端密钥管理

### 监控工具
- **Grafana**: 监控仪表板
- **Prometheus**: 指标收集
- **ELK Stack**: 日志分析

### 安全扫描
- **git-secrets**: 防止密钥提交
- **TruffleHog**: 扫描历史记录中的密钥
- **detect-secrets**: 密钥检测工具

## 💡 最佳实践建议

### 开发环境
1. 使用测试网络和模拟交易
2. 限制测试API的权限
3. 定期清理测试数据
4. 使用不同的测试密钥

### 生产环境
1. 使用专用的生产API密钥
2. 启用所有安全功能
3. 实施完整的监控
4. 建立应急响应流程

### 团队协作
1. 建立密钥分享规范
2. 使用企业级密钥管理
3. 定期进行安全培训
4. 实施代码审查流程

## 📞 紧急联系

如果发现安全问题或需要技术支持，请：

1. **立即停止系统运行**
2. **撤销可能泄露的密钥**
3. **联系技术支持团队**
4. **保留相关日志和证据**

---

**⚠️ 重要提醒**: 安全无小事，请严格按照本指南执行所有安全措施！ 