#!/usr/bin/env python3
"""
Binance-Lighter 套利交易系统统一启动脚本

这是一个专业的加密货币套利交易系统统一入口，集成了完整的系统管理和运行功能。

使用示例:
    python run.py --paper-trading      # 模拟交易模式
    python run.py --dry-run            # 干运行模式
    python run.py --test-system        # 系统测试
    python run.py --status             # 查看运行状态
    python run.py --init-db            # 初始化数据库
"""

import os
import sys
import asyncio
import argparse
import signal
import time
import uvicorn
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入进程锁管理器
try:
    from src.utils.process_lock import ProcessLock, force_cleanup_lock, get_running_instance_info
    PROCESS_LOCK_AVAILABLE = True
except ImportError:
    print("⚠️ 警告: 无法导入进程锁模块，将跳过重复启动检查")
    PROCESS_LOCK_AVAILABLE = False

# 导入核心模块
from src.utils.logger import setup_logging, setup_logger
from src.utils.config_loader import ConfigLoader
from src.database.database import DatabaseManager
from src.database.trade_recorder import TradeRecorder
from src.arbitrage.engine import ArbitrageEngine
from src.web.app import create_web_app

# 设置日志
logger = setup_logger(__name__)


class UnifiedArbitrageSystem:
    """统一的套利交易系统"""

    def __init__(self, config_path: str = "config/settings.yaml", args=None):
        self.config_path = config_path
        self.args = args or argparse.Namespace()
        self.config = None
        self.db_manager = None
        self.trade_recorder = None
        self.engine = None
        self.web_app = None
        self.web_server = None
        self.web_server_task = None
        self.engine_task = None
        self.is_running = False
        self.actual_web_port = None
        self._shutdown_event = None

        # 交易模式设置
        self.paper_trading = getattr(args, 'paper_trading', None)
        self.enable_trading = getattr(args, 'enable_trading', None)

    async def initialize(self):
        """初始化系统"""
        logger.info("🚀 开始初始化Binance-Lighter套利交易系统...")

        try:
            # 1. 加载配置
            self.config = ConfigLoader.load_config(self.config_path)
            logger.info("✅ 配置加载完成")

            # 2. 初始化数据库
            db_path = self.config.get('database', {}).get('path', 'data/arbitrage.db')
            # 确保数据目录存在
            Path(db_path).parent.mkdir(parents=True, exist_ok=True)

            self.db_manager = DatabaseManager(db_path)
            await self.db_manager.initialize()
            logger.info(f"✅ 数据库初始化完成: {db_path}")

            # 3. 初始化交易记录器
            self.trade_recorder = TradeRecorder(self.db_manager)
            logger.info("✅ 交易记录器初始化完成")

            # 4. 初始化套利引擎
            self.engine = ArbitrageEngine(self.config)

            # 注入数据库组件
            self.engine.db_manager = self.db_manager
            self.engine.trade_recorder = self.trade_recorder

            # 从配置和命令行参数设置交易模式
            trading_config = self.config.get('trading', {})

            # 优先使用命令行参数，否则使用配置文件
            if self.paper_trading is not None:
                self.engine.is_paper_trading = self.paper_trading
            else:
                self.engine.is_paper_trading = trading_config.get('paper_trading', True)

            # 交易启用逻辑
            if self.enable_trading is True:
                self.engine.is_trading_enabled = True
            elif self.paper_trading is True:
                self.engine.is_trading_enabled = True
            else:
                self.engine.is_trading_enabled = trading_config.get('enabled', False)

            logger.info(f"✅ 套利引擎初始化完成 (纸上交易: {self.engine.is_paper_trading})")

            # 5. 创建Web应用（如果未禁用）
            if not getattr(self.args, 'no_web', False):
                self.web_app = create_web_app(self.engine)
                logger.info("✅ Web监控界面创建完成")

            # 6. 设置信号处理
            self._setup_signal_handlers()

            logger.info("🎉 系统初始化完成！")

        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            raise

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭...")
            self.is_running = False
            if hasattr(self, '_shutdown_event') and self._shutdown_event:
                self._shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 创建关闭事件
        self._shutdown_event = asyncio.Event()

    def _find_available_port(self, start_port: int, max_attempts: int = 10) -> int:
        """查找可用端口"""
        import socket

        for i in range(max_attempts):
            port = start_port + i
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue

        raise Exception(f"无法找到可用端口，尝试了 {start_port} 到 {start_port + max_attempts - 1}")

    async def start_web_server(self):
        """启动Web服务器"""
        if not self.web_app:
            return

        web_config = self.config.get('monitoring', {})
        host = getattr(self.args, 'web_host', web_config.get('web_host', '127.0.0.1'))
        initial_port = getattr(self.args, 'web_port', web_config.get('web_port', 8000))

        # 查找可用端口
        try:
            port = self._find_available_port(initial_port)
            if port != initial_port:
                logger.info(f"🔄 端口 {initial_port} 被占用，使用端口 {port}")
        except Exception as e:
            logger.error(f"❌ 无法找到可用端口: {e}")
            raise

        logger.info(f"🌐 启动Web服务器: http://{host}:{port}")

        try:
            # 创建服务器配置
            config = uvicorn.Config(
                app=self.web_app,
                host=host,
                port=port,
                log_level="info",
                access_log=True
            )

            # 创建服务器实例
            self.web_server = uvicorn.Server(config)

            # 在后台任务中启动服务器
            self.web_server_task = asyncio.create_task(self.web_server.serve())

            # 等待服务器真正启动
            max_wait = 10
            wait_time = 0
            while not self.web_server.started and wait_time < max_wait:
                await asyncio.sleep(0.1)
                wait_time += 0.1

            if self.web_server.started:
                logger.info(f"✅ Web服务器启动成功: http://{host}:{port}")
                self.actual_web_port = port
            else:
                logger.warning(f"⚠️ Web服务器启动超时，但任务已创建: http://{host}:{port}")

        except Exception as e:
            logger.error(f"❌ Web服务器启动失败: {e}")
            raise

    async def start_engine(self):
        """启动套利引擎"""
        logger.info("🔄 启动套利引擎...")

        try:
            # 初始化引擎（如果还没有初始化）
            if not self.engine.is_initialized:
                await self.engine.initialize()

            # 设置引擎为运行状态
            self.engine.is_running = True
            self.engine.stats['start_time'] = time.time()

            # 在后台启动引擎任务
            self.engine_task = asyncio.create_task(self._run_engine_tasks())

            logger.info("✅ 套利引擎启动成功")

        except Exception as e:
            logger.error(f"❌ 套利引擎启动失败: {e}")
            raise

    async def _run_engine_tasks(self):
        """运行引擎后台任务"""
        tasks = []
        try:
            # 启动定期检查任务
            if hasattr(self.engine, '_periodic_check'):
                periodic_task = asyncio.create_task(self.engine._periodic_check())
                tasks.append(periodic_task)

            # 启动系统监控任务
            if hasattr(self.engine, '_system_monitoring_task'):
                monitoring_task = asyncio.create_task(self.engine._system_monitoring_task())
                tasks.append(monitoring_task)

            # 启动数据清理任务
            if hasattr(self.engine, '_data_cleanup_task'):
                cleanup_task = asyncio.create_task(self.engine._data_cleanup_task())
                tasks.append(cleanup_task)

            # 等待停止信号或关闭事件
            try:
                done, pending = await asyncio.wait(
                    tasks + [asyncio.create_task(self._shutdown_event.wait())],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # 取消所有待处理的任务
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            except asyncio.CancelledError:
                logger.info("套利引擎任务被取消")

        except Exception as e:
            logger.error(f"引擎后台任务异常: {e}")
        finally:
            # 确保所有任务都被取消
            for task in tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

    async def start(self):
        """启动完整系统"""
        logger.info("🎯 启动完整套利交易系统...")

        try:
            # 初始化系统
            await self.initialize()

            # 启动套利引擎
            await self.start_engine()

            # 启动Web服务器
            await self.start_web_server()

            # 设置运行状态
            self.is_running = True

            logger.info("🎉 系统启动完成！")
            self._print_system_status()

            # 显示实时状态
            await self.show_real_time_status()

        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            await self.shutdown()
            raise

    def _print_system_status(self):
        """打印系统状态"""
        logger.info("=" * 60)
        logger.info("📊 系统状态:")
        logger.info(f"   🔧 套利引擎: {'运行中' if self.engine.is_running else '已停止'}")
        logger.info(f"   💰 交易模式: {'纸上交易' if self.engine.is_paper_trading else '实盘交易'}")
        logger.info(f"   🔄 交易状态: {'启用' if self.engine.is_trading_enabled else '禁用'}")

        # 使用实际的Web服务器端口
        if self.web_app:
            web_host = getattr(self.args, 'web_host', self.config.get('monitoring', {}).get('web_host', '127.0.0.1'))
            web_port = self.actual_web_port or getattr(self.args, 'web_port', self.config.get('monitoring', {}).get('web_port', 8000))
            logger.info(f"   🌐 Web界面: http://{web_host}:{web_port}")
        logger.info("=" * 60)

    async def show_real_time_status(self):
        """显示实时状态"""
        logger.info("📈 开始显示实时状态 (按 Ctrl+C 停止)...")

        try:
            while self.is_running:
                try:
                    # 获取最近交易
                    recent_trades = await self.db_manager.get_recent_trades(5)

                    # 获取今日统计
                    today = datetime.now()
                    if self.trade_recorder:
                        daily_performance = await self.trade_recorder.calculate_daily_performance(today)

                        logger.info("=" * 50)
                        logger.info(f"⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                        logger.info(f"💰 今日盈利: {daily_performance.total_profit} USDT")
                        logger.info(f"📊 今日交易: {daily_performance.total_trades} 次")
                        logger.info(f"✅ 成功率: {daily_performance.success_rate:.1f}%")
                        logger.info(f"📈 最近交易数: {len(recent_trades)}")

                        # 显示连接状态
                        if hasattr(self.engine, 'connection_status'):
                            conn_status = self.engine.connection_status
                            logger.info(f"🔗 连接状态: Binance={conn_status.get('binance', False)}, Lighter={conn_status.get('lighter', False)}")

                    # 等待30秒或关闭信号
                    try:
                        await asyncio.wait_for(self._shutdown_event.wait(), timeout=30.0)
                        break  # 收到关闭信号
                    except asyncio.TimeoutError:
                        continue  # 超时，继续循环

                except Exception as e:
                    logger.error(f"状态显示循环异常: {e}")
                    await asyncio.sleep(5)

        except KeyboardInterrupt:
            logger.info("用户中断，开始关闭系统...")
            await self.shutdown()
        except Exception as e:
            logger.error(f"状态显示异常: {e}")

    async def shutdown(self):
        """优雅关闭系统"""
        if not self.is_running:
            return

        logger.info("🛑 开始关闭系统...")
        self.is_running = False

        try:
            # 1. 停止套利引擎任务
            if hasattr(self, 'engine_task') and self.engine_task:
                self.engine_task.cancel()
                try:
                    await self.engine_task
                except asyncio.CancelledError:
                    pass
                logger.info("✅ 套利引擎任务已停止")

            # 2. 停止套利引擎
            if self.engine:
                self.engine.is_running = False
                self.engine.is_trading_enabled = False
                # 取消所有活跃订单
                if hasattr(self.engine, '_cancel_all_active_orders'):
                    await self.engine._cancel_all_active_orders()
                # 停止客户端
                if hasattr(self.engine, 'binance_client') and self.engine.binance_client:
                    await self.engine.binance_client.close()
                if hasattr(self.engine, 'lighter_client') and self.engine.lighter_client:
                    await self.engine.lighter_client.close()
                logger.info("✅ 套利引擎已停止")

            # 3. 停止Web服务器
            if self.web_server:
                self.web_server.should_exit = True
                logger.info("✅ Web服务器已停止")

            # 4. 关闭数据库连接
            if self.db_manager:
                await self.db_manager.close()
                logger.info("✅ 数据库连接已关闭")

            logger.info("🎉 系统关闭完成")

        except Exception as e:
            logger.error(f"❌ 系统关闭异常: {e}")


def check_single_instance() -> Optional[ProcessLock]:
    """
    检查是否已有实例在运行

    Returns:
        进程锁对象或None（如果进程锁不可用）
    """
    if not PROCESS_LOCK_AVAILABLE:
        print("⚠️ 进程锁不可用，跳过重复启动检查")
        return None

    try:
        process_lock = ProcessLock("data/arbitrage.lock")

        if not process_lock.acquire():
            # 显示额外的帮助信息
            print("\n🔍 可以使用以下命令查看运行状态:")
            print(f"   python {sys.argv[0]} --status")
            print(f"   python {sys.argv[0]} --force-cleanup")
            sys.exit(1)

        return process_lock

    except Exception as e:
        print(f"❌ 进程锁检查失败: {e}")
        return None


def show_running_status() -> None:
    """显示运行中实例的状态"""
    if not PROCESS_LOCK_AVAILABLE:
        print("⚠️ 进程锁模块不可用")
        return

    info = get_running_instance_info("data/arbitrage.lock")

    if info:
        print("📊 运行中的套利系统实例:")
        print(f"   PID: {info['pid']}")
        print(f"   进程名: {info['name']}")
        print(f"   命令行: {info['cmdline']}")
        print(f"   CPU使用率: {info['cpu_percent']:.1f}%")
        print(f"   内存使用率: {info['memory_percent']:.1f}%")
        print(f"   状态: {info['status']}")

        # 检查Web界面是否可用
        try:
            import requests
            response = requests.get("http://localhost:8000/api/status", timeout=5)
            if response.status_code == 200:
                print("   🌐 Web界面: http://localhost:8000 (可访问)")
            else:
                print("   🌐 Web界面: http://localhost:8000 (无响应)")
        except Exception:
            print("   🌐 Web界面: 状态未知")

    else:
        print("📭 没有检测到运行中的套利系统实例")


def force_cleanup() -> None:
    """强制清理锁文件"""
    if not PROCESS_LOCK_AVAILABLE:
        print("⚠️ 进程锁模块不可用")
        return

    if force_cleanup_lock("data/arbitrage.lock"):
        print("✅ 锁文件清理成功")
    else:
        print("❌ 锁文件清理失败")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Binance-Lighter 套利交易系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --paper-trading          启动模拟交易模式 (推荐首次使用)
  %(prog)s --dry-run                干运行模式，只检查配置
  %(prog)s --test-system            测试系统配置和连接
  %(prog)s --config custom.yaml     使用自定义配置文件
  %(prog)s --init-db                初始化数据库
  %(prog)s --status                 查看运行中实例状态
  %(prog)s --force-cleanup          强制清理锁文件

注意:
  首次使用建议先运行 --test-system 测试配置
  然后使用 --paper-trading 模式进行测试
        """
    )

    # 基本选项
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/settings.yaml',
        help='配置文件路径 (默认: config/settings.yaml)'
    )

    parser.add_argument(
        '--log-level', '-l',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )

    # 运行模式
    parser.add_argument(
        '--paper-trading', '-p',
        action='store_true',
        help='启用模拟交易模式 (建议首次使用)'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式：只检查配置，不启动系统'
    )

    parser.add_argument(
        '--test-system',
        action='store_true',
        help='测试系统配置和连接'
    )

    # Web界面配置
    parser.add_argument(
        '--web-port',
        type=int,
        default=8000,
        help='Web监控界面端口 (默认: 8000)'
    )

    parser.add_argument(
        '--web-host',
        type=str,
        default='localhost',
        help='Web监控界面主机 (默认: localhost)'
    )

    parser.add_argument(
        '--no-web',
        action='store_true',
        help='禁用Web监控界面'
    )

    # 数据库管理
    parser.add_argument(
        '--init-db',
        action='store_true',
        help='初始化数据库'
    )

    parser.add_argument(
        '--backup-db',
        action='store_true',
        help='备份数据库'
    )

    parser.add_argument(
        '--cleanup-data',
        type=int,
        metavar='DAYS',
        help='清理指定天数前的数据'
    )

    # 系统管理
    parser.add_argument(
        '--status',
        action='store_true',
        help='查看运行中实例的状态'
    )

    parser.add_argument(
        '--force-cleanup',
        action='store_true',
        help='强制清理进程锁文件'
    )

    # 其他选项
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='%(prog)s 1.0.0'
    )

    parser.add_argument('--test-realtime', action='store_true',
                       help='测试实时价格更新性能')

    return parser.parse_args()


def validate_environment():
    """验证运行环境"""
    print("🔍 检查运行环境...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

    # 检查必要的目录
    required_dirs = ["config", "logs", "src", "data"]
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            print(f"📁 创建目录: {dir_path}")
            dir_path.mkdir(parents=True, exist_ok=True)
        else:
            print(f"✅ 目录存在: {dir_name}")

    # 检查配置文件
    config_file = project_root / "config" / "settings.yaml"
    if not config_file.exists():
        print(f"❌ 错误: 配置文件不存在 {config_file}")
        print("   请确保config/settings.yaml文件存在并正确配置")
        sys.exit(1)
    print(f"✅ 配置文件: {config_file}")

    print("✅ 环境检查通过")


def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")

    required_packages = [
        "ccxt", "asyncio", "aiohttp", "websockets",
        "numpy", "pandas", "yaml", "fastapi",
        "uvicorn", "structlog", "psutil", "decimal"
    ]

    missing_packages = []

    for package in required_packages:
        try:
            if package == "yaml":
                __import__("yaml")
            elif package == "decimal":
                import decimal
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")

    if missing_packages:
        print("\n❌ 错误: 缺少必要的依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("   pip install -r requirements.txt")
        sys.exit(1)

    print("✅ 依赖检查通过")


def setup_environment_variables(args):
    """设置环境变量"""
    print("\n⚙️  设置环境变量...")

    if args.paper_trading:
        os.environ["PAPER_TRADING"] = "true"
        print("📝 启用模拟交易模式")

    if args.log_level:
        os.environ["LOG_LEVEL"] = args.log_level
        print(f"📝 设置日志级别: {args.log_level}")

    if args.web_port:
        os.environ["WEB_PORT"] = str(args.web_port)
        print(f"📝 设置Web端口: {args.web_port}")

    if args.web_host:
        os.environ["WEB_HOST"] = args.web_host
        print(f"📝 设置Web主机: {args.web_host}")

    if args.no_web:
        os.environ["NO_WEB"] = "true"
        print("📝 禁用Web监控界面")

    if args.dry_run:
        os.environ["DRY_RUN"] = "true"
        print("📝 启用干运行模式")


def print_startup_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚀 Binance-Lighter 套利交易系统                           ║
║                                                              ║
║    版本: v1.0.0                                              ║
║    作者: AI Assistant                                        ║
║    描述: 专业的加密货币套利交易系统                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_safety_warning():
    """打印安全警告"""
    warning = """
⚠️  重要安全提醒:
   1. 请确保您已经充分理解套利交易的风险
   2. 建议先在测试环境中运行系统
   3. 请妥善保管您的API密钥和私钥
   4. 建议启用模拟交易模式进行测试 (--paper-trading)
   5. 请定期备份重要数据和配置

💡 提示:
   - 使用 --paper-trading 启用模拟交易
   - 使用 --test-system 测试系统配置
   - 使用 --init-db 初始化数据库
   - 查看 config/settings.yaml 进行详细配置

📖 文档: README.md
🌐 监控界面: http://localhost:8000 (启动后访问)
    """
    print(warning)


async def init_database():
    """初始化数据库"""
    print("\n🗄️  初始化数据库...")
    try:
        from src.database.database import DatabaseManager

        db_manager = DatabaseManager()
        await db_manager.initialize()
        print("✅ 数据库初始化成功")
        await db_manager.close()

    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)


async def backup_database():
    """备份数据库"""
    print("\n💾 备份数据库...")
    try:
        from src.database.database import DatabaseManager

        db_manager = DatabaseManager()
        await db_manager.initialize()
        backup_path = await db_manager.backup_database()
        print(f"✅ 数据库备份成功: {backup_path}")
        await db_manager.close()

    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        sys.exit(1)


async def cleanup_data(days: int):
    """清理旧数据"""
    print(f"\n🧹 清理{days}天前的数据...")
    try:
        from src.database.database import DatabaseManager

        db_manager = DatabaseManager()
        await db_manager.initialize()
        await db_manager.cleanup_old_data(days)
        print(f"✅ 数据清理成功")
        await db_manager.close()

    except Exception as e:
        print(f"❌ 数据清理失败: {e}")
        sys.exit(1)


async def test_system():
    """测试系统配置"""
    print("\n🧪 测试系统配置...")
    try:
        from src.utils.config_loader import ConfigLoader

        # 测试配置加载
        config_loader = ConfigLoader()
        config = config_loader.load_config()
        print("✅ 配置加载成功")

        # 测试配置验证
        if config_loader.validate_config(config):
            print("✅ 配置验证通过")
        else:
            print("❌ 配置验证失败")
            return False

        # 测试数据库连接
        from src.database.database import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        print("✅ 数据库连接成功")
        await db_manager.close()

        # 测试交易所连接 (TODO: 需要实现连接测试)
        print("✅ 系统测试通过")
        return True

    except Exception as e:
        print(f"❌ 系统测试失败: {e}")
        return False


async def run_system(args):
    """运行统一系统"""
    try:
        # 设置配置文件路径
        os.environ["CONFIG_FILE"] = args.config

        print("\n🚀 启动套利交易系统...")

        # 如果是干运行模式，只检查配置
        if args.dry_run:
            print("🔍 干运行模式：检查配置...")
            success = await test_system()
            if success:
                print("✅ 配置检查通过，系统可以正常启动")
            else:
                print("❌ 配置检查失败")
                sys.exit(1)
            return

        # 显示交易模式信息
        if args.paper_trading:
            logger.info("📝 启用模拟交易模式")
        elif args.enable_trading:
            logger.info("💰 启用实际交易模式")
            logger.warning("⚠️ 注意：这是实际交易模式，请确保您了解风险！")
        else:
            logger.info("📝 默认使用模拟交易模式（安全模式）")

        # 创建并启动统一系统
        system = UnifiedArbitrageSystem(
            config_path=args.config,
            args=args
        )

        await system.start()

    except KeyboardInterrupt:
        print("\n\n⏹️  收到停止信号，正在安全关闭系统...")
    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")
        sys.exit(1)


def check_port_availability(host: str = "localhost", port: int = 8000) -> bool:
    """检查端口是否可用"""
    try:
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            result = sock.connect_ex((host, port))
            return result != 0  # 0表示连接成功，端口被占用
    except Exception:
        return False


def handle_port_conflict(port: int = 8000) -> None:
    """处理端口冲突"""
    print(f"\n⚠️  端口 {port} 已被占用！")
    print("\n🔧 解决方案:")
    print(f"   1. 停止占用端口{port}的程序")
    print(f"   2. 使用不同端口: python run.py --web-port 8001")
    print(f"   3. 查看占用端口的进程: lsof -i :{port}")
    print(f"   4. 检查是否有其他套利系统实例在运行: python run.py --status")

    # 尝试寻找可用端口
    for test_port in range(8001, 8010):
        if check_port_availability("localhost", test_port):
            print(f"   💡 建议使用端口 {test_port}: python run.py --web-port {test_port}")
            break


def main_entry():
    """主入口函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 处理特殊命令（不需要进程锁）
        if args.status:
            show_running_status()
            return

        if args.force_cleanup:
            force_cleanup()
            return

        # 打印横幅
        print_startup_banner()

        # 验证环境
        validate_environment()
        check_dependencies()

        # 处理数据库管理命令（不需要进程锁）
        if args.init_db:
            asyncio.run(init_database())
            return

        if args.backup_db:
            asyncio.run(backup_database())
            return

        if args.cleanup_data:
            asyncio.run(cleanup_data(args.cleanup_data))
            return

        if args.test_system:
            success = asyncio.run(test_system())
            sys.exit(0 if success else 1)

        # 🔒 检查单实例运行（只有在启动系统时才需要）
        process_lock = None
        if not args.dry_run:  # dry-run模式不需要进程锁
            print("\n🔍 检查是否已有实例在运行...")
            process_lock = check_single_instance()
            if process_lock:
                print("✅ 进程锁获取成功，系统可以启动")

        try:
            # 设置环境变量
            setup_environment_variables(args)

            # 打印安全警告
            print_safety_warning()

            # 等待用户确认（非干运行和模拟交易模式）
            if not args.dry_run and not args.paper_trading:
                try:
                    input("\n按 Enter 键继续，或 Ctrl+C 取消...")
                except KeyboardInterrupt:
                    print("\n\n👋 用户取消，退出程序")
                    return

            # 处理实时价格更新性能测试
            if args.test_realtime:
                print("🚀 正在测试实时价格更新性能...")
                print("📊 将启动系统并测试WebSocket推送延迟")
                print("💡 请在浏览器中打开 http://localhost:8000 观察更新频率")
                print("⏱️  预期价格更新频率: 每1秒")
                print("📈 预期状态更新频率: 每5秒")
                print("🔗 预期连接状态频率: 每3秒")
                print("=" * 50)

            # 检查端口可用性
            if not check_port_availability():
                handle_port_conflict()

            # 运行系统
            asyncio.run(run_system(args))

        finally:
            # 确保释放进程锁
            if process_lock:
                process_lock.release()
                print("🔓 进程锁已释放")

    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main_entry()