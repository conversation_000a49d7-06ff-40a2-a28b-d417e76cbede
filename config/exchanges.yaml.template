# 交易所配置模板文件
# 使用说明：
# 1. 复制此文件为 exchanges.yaml
# 2. 填入您的真实API密钥和配置
# 3. exchanges.yaml 已被 .gitignore 忽略，不会提交到版本控制

# Binance 配置
binance:
  api_key: "YOUR_BINANCE_API_KEY_HERE"
  secret: "YOUR_BINANCE_SECRET_KEY_HERE"
  sandbox: true          # 生产环境设为 false
  testnet: true          # 生产环境设为 false
  timeout: 30            # 请求超时时间(秒)
  rateLimit: true        # 是否启用速率限制
  
  # 测试网络配置
  testnet_url: "https://testnet.binance.vision"
  
  # 生产环境配置
  # 生产环境请设置 sandbox: false, testnet: false
  # production_url: "https://api.binance.com"

# Lighter 配置  
lighter:
  api_url: "https://api.lighter.xyz"
  private_key: "YOUR_LIGHTER_PRIVATE_KEY_HERE"
  account_index: 595
  api_key_index: 1
  timeout: 30            # 请求超时时间(秒)
  max_retries: 3         # 最大重试次数
  
  # WebSocket配置
  ws_url: "wss://api.lighter.xyz/v1/ws"
  heartbeat_interval: 30
  
  # 测试环境配置
  testnet_url: "https://api-testnet.lighter.xyz"

# 安全配置
security:
  # API密钥加密 (可选)
  encrypt_keys: false
  encryption_key: "YOUR_ENCRYPTION_KEY_HERE"
  
  # IP白名单 (可选)
  ip_whitelist:
    - "127.0.0.1"
    - "YOUR_SERVER_IP_HERE"

# 注意事项：
# 1. 请妥善保管您的API密钥，不要分享给他人
# 2. 建议在测试环境中充分测试后再使用生产环境
# 3. 定期更换API密钥以提高安全性
# 4. 使用IP白名单限制API访问
# 5. 监控API使用情况，发现异常及时处理 